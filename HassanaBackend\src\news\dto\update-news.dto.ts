import { Field, InputType, PartialType } from '@nestjs/graphql';
import { CreateNewsInput } from './create-news.dto';
import { IsOptional, IsString, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';

@InputType()
export class UpdateNewsInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  title?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  url?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  category?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  summary?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  author?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  source?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  visibility?: Date;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  publication?: Date;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  created_on?: Date;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  created_by?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  updated_on?: Date;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  updated_by?: string;

  // This will be set by the controller after file upload
  featuredImage?: string;
}