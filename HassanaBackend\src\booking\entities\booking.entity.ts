import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';
import { Resource } from '@app/resource/entities/resource.entity';
import { User } from '@app/users/entities/user.entity';
import { BaseEntity } from '@app/BaseEntity';
import { UUID } from 'crypto';

@Entity()
export class Booking extends BaseEntity {
  // @PrimaryGeneratedColumn()
  // id: UUID;

  @Column()
  title: string;

  @Column({ nullable: true })
  details: string;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  registrationDoc?: string;

  // @Column()
  // resourceId: number;

  @Column()
  userId: UUID;

  @Column({ unique: true })
  uid: string;

  @Column()
  start: Date;

  // @Column({ default: "pending" })
  // status: string;

  @Column({ default: 'false' })
  teaBoy: string;

  @Column({ default: 'false' })
  parking: string;

  @Column({ default: 'false' })
  itTechnician: string;

  @Column()
  end: Date;

  // @ManyToOne(() => Resource, (resource) => resource.bookings)
  // @JoinColumn({ name: 'resourceId' })
  // resource: Resource;

  // @ManyToOne(() => User, (user) => user.bookings)
  // @JoinColumn({ name: 'userId' })
  // user: User;
}
