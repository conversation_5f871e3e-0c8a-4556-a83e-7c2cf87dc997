
// import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';


// @Entity({ name: "news" })
// export class ExchangeInfo {
//     @PrimaryGeneratedColumn()
//     id: UUID;

//     @Column()
//     title: string;

//     @Column()
//     url?: string;

//     @Column()
//     category: string;

//     @Column()
//     featuredImage?: string;

//     @Column()
//     summary: string;

//     @Column()
//     author?: string;

//     @Column()
//     source?: string;

//     @Column()
//     status: string;

//     @Column()
//     visibility?: Date;

//     @Column()
//     publication?: Date;

//     @Column()
//     created_on: Date;

//     @Column()
//     created_by: string;

//     @Column()
//     updated_on?: Date;

//     @Column()
//     updated_by?: string;
// }
