import { ObjectType, Field, Int, GraphQLISODateTime } from '@nestjs/graphql';
import { UUID } from 'crypto';
@ObjectType()
export class Announcement {
    @Field((type) => Int)
    id: UUID;

    @Field()
    title: string;

    @Field()
    details: string;

    @Field()
    category: string;

    @Field()
    status: Boolean;

    @Field((type) => GraphQLISODateTime)
    visibility: Date;

    @Field()
    image: string;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;
}
