version: '3.3'
services:
  redis:
    image: redis:latest
    user: redis
    networks:
      - network
    ports:
      - '0.0.0.0:6379:6379'
    command: ['redis-server', '--requirepass', 'foobared']

  # redisinsight:
  #   image: redislabs/redisinsight:latest
  #   ports:
  #     - '8001:8001'

  adminer:
    image: adminer
    restart: always
    ports:
      - 8888:8080
    networks:
      - network

  pgsql:
    image: postgres:12-alpine
    # restart: always
    ports:
      - '5555:5432'
    environment:
      # use a env file for that part later
      # https://docs.docker.com/compose/environment-variables/#the-env_file-configuration-option
      POSTGRES_USER: hassana
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: hassana
      DIALECT: postgres
      TZ: "UTC" 
      PGDATA: /var/lib/postgresql/data/pg_data
    volumes:
      - pg_data:/var/lib/postgresql/data/pg_data
    networks:
      - network

volumes:
  pg_data:
  db:
    driver: local

networks:
  network:
