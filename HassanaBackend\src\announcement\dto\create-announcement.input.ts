import { InputType, Int, Field } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDate, IsNotEmpty, IsString } from 'class-validator';

@InputType()
export class CreateAnnouncementInput {

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  details: string;
  
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  category: string;

  @ApiProperty()
  @IsNotEmpty()
  status: Boolean;

  @ApiProperty()
  @IsNotEmpty()
  // @IsDate()
  visibility: Date;

  // @IsNotEmpty()
  // @IsString()
  @ApiProperty()
  image: string

}
