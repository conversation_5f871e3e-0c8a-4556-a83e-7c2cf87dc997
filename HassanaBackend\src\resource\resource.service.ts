import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateResourceInput } from './dto/create-resource.input';
import { UpdateResourceInput } from './dto/update-resource.input';
import { Resource } from './entities/resource.entity';
import { UUID } from 'crypto';

@Injectable()
export class ResourceService {
  constructor(
    @InjectRepository(Resource)
    private resourceRepository: Repository<Resource>,
  ) {}

  async create(createResourceInput: CreateResourceInput): Promise<Resource> {
    const newResource = this.resourceRepository.create(createResourceInput);
    return await this.resourceRepository.save(newResource);
  }

  async findAll(): Promise<Resource[]> {
    return await this.resourceRepository.find();
  }

  async findOne(id: UUID): Promise<Resource> {
    const resource = await this.resourceRepository.findOne({ where: { id } });
    if (!resource) {
      throw new NotFoundException(`Resource with ID ${id} not found`);
    }
    return resource;
  }

  async update(id: UUID, updateResourceInput: UpdateResourceInput) {
    const existingResource = await this.resourceRepository.findOne({ where: { id } });

    if (existingResource) {
      this.resourceRepository.merge(existingResource, updateResourceInput);
      return this.resourceRepository.save(existingResource);
    }

    return null;
  }

  async remove(id: UUID): Promise<Resource> {
    const resource = await this.findOne(id);
    await this.resourceRepository.remove(resource);
    return resource;
  }
}

// import { Injectable, NotFoundException } from '@nestjs/common';
// import { InjectRepository } from '@nestjs/typeorm';
// import { Repository } from 'typeorm';
// import { CreateResourceInput } from './dto/create-resource.input';
// import { UpdateResourceInput } from './dto/update-resource.input';
// import { Resource } from './entities/resource.entity';

// @Injectable()
// export class ResourceService {
//   constructor(
//     @InjectRepository(Resource)
//     private resourceRepository: Repository<Resource>,
//   ) {}

//   async create(createResourceInput: CreateResourceInput): Promise<Resource> {
//     const newResource = this.resourceRepository.create(createResourceInput);
//     const savedResource = await this.resourceRepository.save(newResource);
//     if (!savedResource) {
//       throw new Error('Error creating resource');
//     }
//     return savedResource;
//   }

//   async findAll(): Promise<Resource[]> {
//     const resources = await this.resourceRepository.find();
//     if (!resources) {
//       throw new Error('Error retrieving resources');
//     }
//     return resources;
//   }

//   async findOne(id: number): Promise<Resource> {
//     const resource = await this.resourceRepository.findOne({ where: { id } });
//     if (!resource) {
//       throw new NotFoundException(`Resource with ID ${id} not found`);
//     }
//     return resource;
//   }

//   async update(
//     id: number,
//     updateResourceInput: UpdateResourceInput,
//   ): Promise<Resource> {
//     const resource = await this.resourceRepository.findOne({ where: { id } });
//     if (!resource) {
//       throw new NotFoundException(`Resource with ID ${id} not found`);
//     }
//     const updatedResource = this.resourceRepository.merge(
//       resource,
//       updateResourceInput,
//     );
//     return this.resourceRepository.save(updatedResource);
//   }

//   async remove(id: number): Promise<Resource> {
//     const resource = await this.resourceRepository.findOne({ where: { id } });
//     if (!resource) {
//       throw new NotFoundException(`Resource with ID ${id} not found`);
//     }
//     await this.resourceRepository.remove(resource);
//     return resource;
//   }
// }

// import { Injectable } from '@nestjs/common';
// import { InjectRepository } from '@nestjs/typeorm';
// import { Repository } from 'typeorm';
// import { CreateResourceInput } from './dto/create-resource.input';
// import { UpdateResourceInput } from './dto/update-resource.input';
// import { Resource } from './entities/resource.entity';
// import { NotFoundException } from '@nestjs/common';

// @Injectable()
// export class ResourceService {
//   constructor(
//     @InjectRepository(Resource)
//     private resourceRepository: Repository<Resource>,
//   ) {}

//   async create(createResourceInput: CreateResourceInput): Promise<Resource> {
//     const newResource = this.resourceRepository.create(createResourceInput);
//     return this.resourceRepository.save(newResource);
//   }

//   async findAll(): Promise<Resource[]> {
//     return this.resourceRepository.find();
//   }

//   async findOne(id: number): Promise<Resource> {
//     return this.resourceRepository.findOne({ where: { id } });
//   }

//   async update(
//     id: number,
//     updateResourceInput: UpdateResourceInput,
//   ): Promise<Resource> {
//     const resource = await this.resourceRepository.preload({
//       id,
//       ...updateResourceInput,
//     });
//     if (!resource) {
//       throw new Error(`Resource #${id} not found`);
//     }
//     return this.resourceRepository.save(resource);
//   }

//   async remove(id: number): Promise<void> {
//     const resource = await this.resourceRepository.findOne({ where: { id } });
//     if (!resource) {
//       throw new NotFoundException(`Resource with ID ${id} not found`);
//     }
//     await this.resourceRepository.remove(resource);
//   }
// }
