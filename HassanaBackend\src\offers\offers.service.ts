import { Injectable } from '@nestjs/common';
import { CreateOffersInput } from './dto/create-offer.dto';
import { UpdateOffersInput } from './dto/update-offer.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Offers } from './entities/offers.entity';
import { Between, Equal, Repository } from 'typeorm';
import { UUID } from 'crypto';
import { OffersViewEntity } from './entities/offers.entity';

@Injectable()
export class OffersService {

  constructor(@InjectRepository(Offers) private readonly offersRepository: Repository<Offers>, @InjectRepository(OffersViewEntity) private readonly offersViewRepository: Repository<OffersViewEntity>) { }

  async create(createOffersInput: CreateOffersInput): Promise<Offers> {
    const create = this.offersRepository.create(createOffersInput);
    const saved = await this.offersRepository.save(create);
    return saved;
  };

  async findAll(userId: UUID, filter: Object): Promise<Offers[]> {
    const offers = await this.offersRepository.createQueryBuilder("offers")
      .leftJoinAndSelect("offers_view", "view", "view.offer_id = offers.id AND view.user_id = :userId", { userId })
      .select([
        "offers.id AS id",
        "offers.name AS name",
        "offers.contact_information AS contact_information",
        "offers.code AS code",
        "offers.expiry_date AS expiry_date",
        "offers.description AS description",
        "offers.status AS status",
        "offers.createdAt AS created_at",
        "CASE WHEN view.id IS NOT NULL THEN true ELSE false END AS is_read"
      ])
      .where(filter)
      .orderBy("offers.createdAt", "DESC")
      .getRawMany();

    return offers;
  };

  async findOne(filter: Object): Promise<Offers> {
    return await this.offersRepository.findOne({ where: filter });
  };

  async createOfferView(offerId: UUID, userId: UUID) {
    return await this.offersViewRepository.upsert({ offer_id: offerId, user_id: userId }, {conflictPaths: ["user_id", "offer_id"]});
  };

  async update(id: UUID, updateOffersInput: UpdateOffersInput) {
    const existingOffer = await this.offersRepository.findOne({ where: { id } });
    if (!existingOffer) throw Error("offer not found")
    this.offersRepository.merge(existingOffer, updateOffersInput);
    return this.offersRepository.save(existingOffer);
  };

  async remove(id: UUID): Promise<Offers | null> {
    const offer = await this.offersRepository.findOne({ where: { id } });
    if (!offer) throw Error("offer not found")
    await this.offersRepository.remove(offer);
    return offer;
  };
}
