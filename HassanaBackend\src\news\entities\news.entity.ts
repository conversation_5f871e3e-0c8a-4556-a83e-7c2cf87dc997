import { BaseEntity } from '@app/BaseEntity';
import { Column, Entity } from 'typeorm';

@Entity({ name: "news" })
export class News extends BaseEntity {
    // BaseEntity already provides id, createdAt, updatedAt
    // so we don't redeclare id here

    @Column({ nullable: false })
    title: string;

    @Column({ nullable: false })
    url: string;

    @Column({ nullable: false })
    category: string;

    @Column({ nullable: false })
    featuredImage: string;

    @Column({ nullable: false })
    summary: string;

    @Column({ nullable: false })
    author: string;

    @Column({ nullable: false })
    source: string;

    @Column({ nullable: false })
    status: string;

    @Column({ type: 'timestamp', nullable: false })
    visibility: Date;

    @Column({ type: 'timestamp', nullable: false })
    publication: Date;

    @Column({ type: 'timestamp', nullable: false })
    created_on: Date;

    @Column({ nullable: false })
    created_by: string;

    @Column({ type: 'timestamp', nullable: false })
    updated_on: Date;

    @Column({ nullable: false })
    updated_by: string;
}