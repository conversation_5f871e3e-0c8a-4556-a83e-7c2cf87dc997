import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, OneToMany, Unique } from 'typeorm';
import { Booking } from '@app/booking/entities/booking.entity';
import { BaseEntity } from '@app/BaseEntity';
import { NotificationViewEntity } from '@app/notification/entities/notification.entity';
import { UserReview } from '@app/user-reviews/entities/user-review.entity';

export enum RolesEnum {
  ADMIN = 'Admin',
  USER = 'User'
}

@Entity()
@Unique("email_unique", ["email"])
export class User extends BaseEntity {

  @Column({ nullable: true })
  profile: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  name_arabic: string;

  @Column({ nullable: true })
  designation: string;

  @Column({ nullable: true })
  designation_arabic: string;

  @Column({ nullable: true })
  department: string;

  @Column({ nullable: true })
  department_arabic: string;

  @Column({ nullable: true })
  bio_link: string;

  @Column({ nullable: true })
  dn: string;

  @Column({nullable: true, default: "male" })
  gender: string;

  @Column({ nullable: true, default: true })
  new_joiner: string;

  @Column({ nullable: true })
  account_expires?: string;

  @Column({ nullable: true })
  user_principal_name: string;

  @Column({ nullable: true, type: "enum", default: "User", enum: RolesEnum  })
  role: string;

  @Column({ default: true })
  status: string;

  @Column({ default: false })
  is_cultural_ambassador: string;

  @Column({ nullable: true })
  activity: string;

  @Column({ nullable: true })
  extension: string;

  // @OneToMany(() => Booking, (booking) => booking.user)
  // bookings: Booking[];
  @OneToMany(() => UserReview, (user_review) => user_review.user_id)
  user_reviews: UserReview[];

  @OneToMany(() => NotificationViewEntity, (notification_view) => notification_view.user)
  notification_view: NotificationViewEntity[];
}

