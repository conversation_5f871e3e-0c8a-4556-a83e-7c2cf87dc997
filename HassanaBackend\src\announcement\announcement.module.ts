import { Module } from '@nestjs/common';
import { AnnouncementService } from './announcement.service';
// import { AnnouncementResolver } from './announcement.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Announcement, AnnouncementViewEntity } from './entities/announcement.entity';
import { AnnouncementController } from './announcement.controller';

@Module({
  imports:[TypeOrmModule.forFeature([Announcement, AnnouncementViewEntity])],
  controllers: [AnnouncementController],
  providers: [ AnnouncementService],
  exports: [AnnouncementService]
})
export class AnnouncementModule {}

