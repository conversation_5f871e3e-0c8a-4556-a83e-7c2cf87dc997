import { Modu<PERSON> } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { NotificationResolver } from './notification.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationEntity, NotificationViewEntity } from './entities/notification.entity';
import { User } from '@app/users/entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([NotificationEntity, NotificationViewEntity, User])],
  controllers: [],
  providers: [NotificationResolver, NotificationService],
  exports: [NotificationService],
})
export class NotificationModule { }
