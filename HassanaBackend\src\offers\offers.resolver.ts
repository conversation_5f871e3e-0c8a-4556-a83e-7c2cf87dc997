import { Resolver, Query, Mutation, Args,ID,  Int } from '@nestjs/graphql';
import { OffersService } from './offers.service';
import { OffersSchema } from './schema/offers.schema';
import { CreateOffersInput } from './dto/create-offer.dto';
import { UpdateOffersInput } from './dto/update-offer.dto';
import { UUID } from 'crypto';
import { Param, Req, UseGuards } from '@nestjs/common';
import { JwtGuard } from '@app/auth/jwt.guard';
import { LessThan, MoreThan } from 'typeorm';

@UseGuards(JwtGuard)
@Resolver(() => OffersSchema)
export class OffersResolver {
  constructor(private readonly offerService: OffersService) { }

  @Mutation(() => OffersSchema)
  async createOffer(@Args('createOffersInput') createOffersInput: CreateOffersInput) {
    try {
      console.log(createOffersInput,"thisvsjkvs")
      const offerExists = await this.offerService.findOne({ status: true, code: createOffersInput.code });
      if(offerExists) throw Error("Sorry! your input code is already registered")
      const offer = await this.offerService.create({ ...createOffersInput, status: true });
      return offer;
    } catch (error) {
      console.log(error.message);
      throw Error(error);
    }
  }

  @Mutation(() => OffersSchema)
  async offerView(@Args('offer_id', { type: () => String }) offer_id: UUID, @Args('user_id', { type: () => String }) user_id: UUID) {
    await this.offerService.createOfferView(offer_id, user_id);
    return await this.offerService.findOne(offer_id);
  }

  @Query(() => [OffersSchema], { name: 'offers' })
  async findAll(@Args('user_id',) user_id: UUID) {
    return await this.offerService.findAll(user_id, {});
  }

  @Query(() => [OffersSchema], { name: 'validOffers' })
  async findValidOffers(@Args('user_id') user_id: UUID) {
    const now = new Date();
    return await this.offerService.findAll(user_id, { expiry_date: MoreThan(now) });
  }

  @Mutation(() => OffersSchema)
  updateOffer(@Args('id', { type: () => ID }) id: UUID, @Args('updateOffersInput') updateOffersInput: UpdateOffersInput) {
    return this.offerService.update(id, updateOffersInput);
  }

  @Mutation(() => OffersSchema)
  removeOffer(@Args('id', { type: () => ID }) id: UUID) {
    return this.offerService.remove(id);
  }
}
