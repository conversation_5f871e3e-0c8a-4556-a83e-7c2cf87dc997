type Library {
  # Example field (placeholder)
  exampleField: Int
}

input CreateLibraryInput {
  # Example field (placeholder)
  exampleField: Int
}

input UpdateLibraryInput {
  id: Int!
}

type Query {
  library: [Library]!
  library(id: Int!): Library
}

type Mutation {
  createLibrary(createLibraryInput: CreateLibraryInput!): Library!
  updateLibrary(updateLibraryInput: UpdateLibraryInput!): Library!
  removeLibrary(id: Int!): Library
}
