import { Resolver, Query, Mutation, Args, Int, ID } from '@nestjs/graphql';
import { BookingService } from './booking.service';
import { CreateBookingInput } from './dto/create-booking.input';
import { UpdateBookingInput } from './dto/update-booking.input';
import { BookingSchema } from './schema/booking.schema';
import { User } from '@app/users/entities/user.entity';
import { ApprovedBookingInput } from './dto/approved-booking.input';
import { UUID } from 'crypto';
@Resolver(() => BookingSchema)
export class BookingResolver {
  constructor(private readonly bookingService: BookingService) { }

  
  @Query(() => [BookingSchema], { name: 'bookings' })
  findAll() {
    return this.bookingService.findAll();
  }

  @Query(() => [BookingSchema], { name: 'bookingsOfTeaBoy' })
  getAllBookingOfTeaBoy() {
    return this.bookingService.getAllBookingTeaBoy();
  }
  
  @Query(() => [BookingSchema], { name: 'bookingsOfUser' })
  findByUser(@Args('id', { type: () => ID }) id: UUID) {
    return this.bookingService.findByUser(id);
  }

  // @Query(() => BookingSchema, { name: 'booking' })
  // findOne(@Args('id', { type: () => Int }) id: number) {
  //   return this.bookingService.findOne(id);
  // }
  
  
  @Mutation(() => BookingSchema)
   createBooking(@Args('CreateBookingInput') updateBookingInput: UpdateBookingInput) {
    return this.bookingService.create(updateBookingInput);

  }
  

  // @Mutation(() => BookingSchema)
  // updateBookingStatus(@Args('updateBookingInput') approvedBookingInput: ApprovedBookingInput) {
  //   return this.bookingService.updateStatus(
  //     approvedBookingInput.id,
  //     approvedBookingInput,
  //   );
  // }

  // @Mutation(() => BookingSchema)
  // updateBooking(
  //   @Args('updateBookingInput') updateBookingInput: UpdateBookingInput,
  // ) {
  //   return this.bookingService.update(
  //     updateBookingInput.id,
  //     updateBookingInput,
  //   );
  // }

  // @Mutation(() => Boolean)
  // removeBooking(@Args('id', { type: () => Int }) id: number) {
  //   return this.bookingService.remove(id);
  // }
}
