import { Resolver, Query, Mutation, Args, Int, ID } from '@nestjs/graphql';
import { ResourceService } from './resource.service';
import { CreateResourceInput } from './dto/create-resource.input';
import { UpdateResourceInput } from './dto/update-resource.input';
import { ResourceSchema } from './schema/resource.schema';
import { UUID } from 'crypto';

@Resolver(() => ResourceSchema)
export class ResourceResolver {
  constructor(private readonly resourceService: ResourceService) {}

  @Mutation(() => ResourceSchema)
  async createResource(
    @Args('createResourceInput') createResourceInput: CreateResourceInput,
  ) {
    return await this.resourceService.create(createResourceInput);
  }

  @Query(() => [ResourceSchema], { name: 'allResources' }) // changed name to 'allResources'
  async findAll() {
    return await this.resourceService.findAll();
  }

  @Query(() => ResourceSchema, { name: 'singleResource' }) // changed name to 'singleResource'
  async findOne(@Args('id', { type: () => ID }) id: UUID) {
    return await this.resourceService.findOne(id);
  }

  @Mutation(() => ResourceSchema)
  updateResource(
    @Args('updateResourceInput') updateResourceInput: UpdateResourceInput,
  ) {
    return this.resourceService.update(
      updateResourceInput.id,
      updateResourceInput,
    );
  }

  @Mutation(() => ResourceSchema)
  removeResource(@Args('id', { type: () => ID}) id: UUID) {
    return this.resourceService.remove(id);
  }
}
// import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
// import { ResourceService } from './resource.service';
// import { Resource } from './entities/resource.entity';
// import { CreateResourceInput } from './dto/create-resource.input';
// import { UpdateResourceInput } from './dto/update-resource.input';

// @Resolver(() => Resource)
// export class ResourceResolver {
//   constructor(private readonly resourceService: ResourceService) {}

//   @Mutation(() => Resource)
//   createResource(
//     @Args('createResourceInput') createResourceInput: CreateResourceInput,
//   ) {
//     return this.resourceService.create(createResourceInput);
//   }

//   @Query(() => [Resource], { name: 'allResources' }) // changed name to 'allResources'
//   findAll() {
//     return this.resourceService.findAll();
//   }

//   @Query(() => Resource, { name: 'singleResource' }) // changed name to 'singleResource'
//   findOne(@Args('id', { type: () => Int }) id: number) {
//     return this.resourceService.findOne(id);
//   }

//   @Mutation(() => Resource)
//   updateResource(
//     @Args('updateResourceInput') updateResourceInput: UpdateResourceInput,
//   ) {
//     return this.resourceService.update(
//       updateResourceInput.id,
//       updateResourceInput,
//     );
//   }

//   @Mutation(() => Resource)
//   removeResource(@Args('id', { type: () => Int }) id: number) {
//     return this.resourceService.remove(id);
//   }
// }
