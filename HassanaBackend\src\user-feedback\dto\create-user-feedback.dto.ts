// import { InputType, Int, Field } from '@nestjs/graphql';
import { IsNotEmpty, IsString } from 'class-validator';
import { FeedbackType } from '../entities/user-feedback.entity';
import { Field } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';

// @InputType()
export class CreateUserFeedbackDto {

  @ApiProperty({ example: "complaint | suggestion", enum: ["complaint", "suggestion"] })
  @IsString()
  @IsNotEmpty()
  type: FeedbackType;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  description: string;

}

