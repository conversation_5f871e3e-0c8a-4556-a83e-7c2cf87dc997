import { BaseEntity } from "@app/BaseEntity";
import { User } from "@app/users/entities/user.entity";
import { Field, Int, ObjectType } from "@nestjs/graphql";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { UUID } from "crypto";
import { Column, Decimal128, Entity, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from "typeorm";

@ObjectType()
@Entity()
export class UserReview extends BaseEntity {

  @Field({nullable: true})
  @Column({nullable: true})
  review: string;

  @Field()
  @Column({nullable: true})
  @Min(0)
  @Max(5)
  rating: number

  @Field()
  @Column()
  @ManyToOne(() => User)
  @JoinColumn({ name: "user_id" })
  user_id: UUID

  // @Field()
  @Column()
  @ManyToOne(() => User)
  @JoinColumn({ name: "created_by" })
  created_by: UUID

}