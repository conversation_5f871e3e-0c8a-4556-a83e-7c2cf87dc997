import { HttpException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateQuoteInput } from './dto/create-quote.input';
import { UpdateQuoteInput } from './dto/update-quote.input';
import { InjectRepository } from '@nestjs/typeorm';
import { QuoteEntity } from './entities/quote.entity';
import {
  Between,
  Equal,
  LessThan,
  LessThanOrEqual,
  MoreThan,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import { equal } from 'assert';
import { UUID } from 'crypto';

@Injectable()
export class QuoteService {
  constructor(
    @InjectRepository(QuoteEntity)
    private readonly quoteRepository: Repository<QuoteEntity>,
  ) {}

  async create(createQuoteInput: CreateQuoteInput): Promise<QuoteEntity> {
    try {
      const existingQuote = await this.quoteRepository.findOne({
        where: { visibilityStart: Equal(createQuoteInput.visibilityStart) },
      });
      if (!existingQuote) {
        const response = await this.quoteRepository.save(createQuoteInput);
        return response;
      }
      throw new Error('quote alreay exist on this visibility');
    } catch (error) {
      console.log('error in service', error);
      return error;
    }
  }

  async findAll() {
    try {
      let response = await this.quoteRepository.find();
      // console.log(response);

      return response;
    } catch (error) {
      console.log(error.message);
      throw Error(error);
    }
  }

  async findOne(id: number) {
    return `This action returns a #${id} quote`;
  }

  async update(id: UUID, updateQuoteInput: UpdateQuoteInput) {
    try {
      let oldData = await this.quoteRepository.findOne({ where: { id: id } });
      console.log(oldData);

      if (!oldData) throw new NotFoundException('Quote not found');

      if (oldData) {
        this.quoteRepository.merge(oldData, updateQuoteInput);
        let newData = await this.quoteRepository.save(updateQuoteInput);
        return newData;
      }
    } catch (error) {
      throw new Error(error.message);
    }
  }

  async remove(id: UUID) {
    try {
      let data = await this.quoteRepository.findOne({ where: { id: id } });

      if (!data) throw new NotFoundException('Quote not found');

      if (data) {
        return await this.quoteRepository.remove(data);
      }
    } catch (error) {
      throw new Error(error.message);
    }
  }

  async findByVisibility(): Promise<QuoteEntity> | null {
    const todaysDate = new Date();
    const dateStringify = todaysDate.toISOString();
    const splitting = dateStringify.split('T');
    const today = `${splitting[0]}`;
    // const today = `2024-05-07T00:00:00.000Z`

    const result = await this.quoteRepository.findOne({
      where: {
        visibilityStart: LessThanOrEqual(today),
        visibilityEnd: MoreThanOrEqual(today),
        status: true
      },
      order: {
        visibilityStart: 'DESC',
      },
    });
    console.log(result);
    return result;
  }
}
