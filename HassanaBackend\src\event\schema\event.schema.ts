import { ObjectType, Field, Int, ID } from '@nestjs/graphql';
import { UUID } from 'crypto';

@ObjectType()
export class Event {
    @Field((type) => ID)
    id: UUID;

    @Field()
    title: string;

    @Field()
    details: string;

    // @Field((type) => Category)
    @Field()
    category: string;

    @Field()
    status: Boolean;

    @Field()
    date: Date;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;
}
