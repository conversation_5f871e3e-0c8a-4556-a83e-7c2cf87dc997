import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { log } from 'console';
import { MoreThan, Repository } from 'typeorm';
// import { data } from './data';
import { existsSync, unlinkSync } from 'fs';
import { UpdateUserInput } from './dto/update-user.input';
import { User } from './entities/user.entity';
import ldap = require('ldapjs');
import ActiveDirectory = require('activedirectory2');
import path = require('path');
import { UUID } from 'crypto';
import { UserSchema } from './schema/user.schema';
@Injectable()
export class UserService {
  private readonly LDAP_URL = process.env.LDAP_URL || 'ldap://10.0.1.2';
  private readonly BASE_DN = 'dc=hassana,dc=local';

  constructor(
    @InjectRepository(User)
    public readonly userRepo: Repository<User>,
  ) { }

  async userAuth(username: string, password: string): Promise<any> {
    log('LDAP: ' + this.LDAP_URL);

    return new Promise(async (resolve, reject) => {
      try {
        const authResponse = await this.authenticateWithAD(username, password);
        console.log('authResponse of active directory: ', authResponse);

        if (authResponse.auth) {
          const userData = await this.searchUserInLDAP(username, password);

          resolve(userData);
        } else {
          reject({
            code: 403,
            message: 'Authentication failed',
            auth: authResponse.auth,
          });
        }
      } catch (error) {
        console.error('Error during user authentication:', error);
        reject({ code: 500, message: 'Internal Server Error', error: error });
      }
    });
  }

  private async authenticateWithAD(
    username: string,
    password: string,
  ): Promise<{ auth: boolean; error?: any }> {

    if (process.env.MOCK_RESPONSE == 'true') return { auth: true }

    const adConfig = {
      url: this.LDAP_URL,
      baseDN: this.BASE_DN,
      username,
      password,
    };
    const ad = new ActiveDirectory(adConfig);

    return new Promise((resolve, reject) => {
      ad.authenticate(username, password, (err, auth) => {
        if (err) {
          reject({ auth: false, error: err });
          console.log('user authentication: ' + auth);
        } else {
          resolve({ auth });
          console.log('user authentication: ' + auth);
        }
      });
    });
  }

  private async searchUserInLDAP(
    username: string,
    password: string,
  ): Promise<any> {

    if (process.env.MOCK_RESPONSE == 'true') {
      let user: object;
      return user = {
        dn: 'HICUAT',
        name: username,
        userPrincipalName: username
      };
    };

    const client = ldap.createClient({
      url: this.LDAP_URL,
      bindDN: this.BASE_DN,
    });
    return new Promise((resolve, reject) => {
      Logger.log('searching user data in ldap...');

      client.bind(username, password, async (bindErr) => {
        if (bindErr) {
          client.unbind();
          reject({ code: 402, message: 'Client binding failed' });
          return;
        }
        const searchOperations: object = {
          filter: `(userPrincipalName=${username})`,
          scope: 'sub',
          attributes: ['dn', 'name', 'accountExpires', 'userPrincipalName'],
        };

        client.search(this.BASE_DN, searchOperations, (err, res) => {
          if (err) {
            client.unbind();
            console.log('Error during search in client.search: ', err.message);

            reject({
              code: 405,
              message: 'Error during search',
              error: err.message,
            });
            return;
          }
          console.log('searching user data in client.search ldap...');

          this.handleLDAPSearchResults(res, client, resolve, reject);
        });
      });
    });
  }

  private handleLDAPSearchResults(
    res: ldap.SearchCallbackResponse,
    client: ldap.Client,
    resolve: Function,
    reject: Function,
  ): void {
    let user = { dn: '', name: '', accountExpires: '', userPrincipalName: '' };

    res.on('searchEntry', (entry) => {
      user = {
        accountExpires: entry.attributes.find(
          (attr) => attr.type === 'accountExpires',
        )?.values[0],
        userPrincipalName: entry.attributes
          .find((attr) => attr.type === 'userPrincipalName')
          ?.values[0].toLowerCase(),
        name: entry.attributes.find((attr) => attr.type === 'name')?.values[0],
        dn: entry.attributes.find((attr) => attr.type === 'dn')?.values[0],
      };
    });

    res.on('error', (searchErr) => {
      client.unbind();
      console.log('Error during search in res.on: ', searchErr.message);
      reject({
        code: 405,
        message: 'Error during search',
        error: searchErr.message,
      });
    });

    res.on('end', () => {
      console.log('User data found in LDAP: ', user);
      // client.unbind();
      resolve(user);
    });
  }

  async getNameGender(name: string): Promise<string> {
    try {
      const personName = name.includes(' ') ? name.split(' ')[0] : name;
      const response = await axios.get(
        `https://api.genderize.io/?name=${personName}`,
      );
      const data = response.data;

      return data.gender;
    } catch (error) {
      console.error('Error fetching gender information:', error.message);
    }
  }

  async createNewUser(newUser: any): Promise<User> {
    try {
      let user: User = new User();
      user.dn = newUser.dn;
      user.name = newUser.name;
      user.gender = await this.getNameGender(newUser.name);
      user.account_expires = newUser.accountExpires;
      user.user_principal_name = newUser.userPrincipalName;
      user.role = 'User';

      return this.userRepo.save(user);
    } catch (error) {
      console.log('error in user.service.ts > ln:205', error);
      throw Error(error);
    }
    // return user;
  }

  async update(id: UUID, updateUserInput: UpdateUserInput) {
    const existingUser = await this.userRepo.findOne({ where: { id } });

    if (existingUser) {
      console.log('profile checking for delete', updateUserInput.profile);
      if (updateUserInput.profile != undefined) {
        const imageInServer = path.join(__dirname, '../../resource/' + existingUser.profile);
        if (existsSync(imageInServer)) {
          console.log('deleting Image in server ...: ' + imageInServer);
          unlinkSync(imageInServer);
        }
      }

      let updatedUser = this.userRepo.merge(existingUser, updateUserInput);
      let updateData = await this.userRepo.save(updatedUser);

      if (updateData) {
        updateData.profile = `${process.env.SERVER_URL}/${updateData.profile}`;
      }
      return updateData;
    }
    return null;
  }

  async findNewUsers(days: number): Promise<User[]> {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - days);

    // Assuming User entity has a 'createdAt' field
    const users = await this.userRepo.find({
      where: {
        new_joiner: 'true',
        // createdAt: MoreThan(oneWeekAgo),
      },
    });

    if (users && users.length > 0) {
      users.forEach((user) => {
        user.profile != null
          ? (user.profile = `${process.env.SERVER_URL}/${user.profile}`)
          : null;
      });
    }

    return users;
  }

  async findAllCulturalAmbassadors(): Promise<User[]> {

    const users = await this.userRepo.find({
      where: {
        is_cultural_ambassador: 'true',
      },
    });

    if (users && users.length > 0) {
      users.forEach((user) => {
        user.profile != null
          ? (user.profile = `${process.env.SERVER_URL}/${user.profile}`)
          : null;
      });
    }

    return users;
  }

  async findUserById(id: UUID): Promise<User> {
    let user: User = await this.userRepo.findOne({
      where: {
        id: id,
      },
    });
    if (user) {
      user.profile = `${process.env.SERVER_URL}/${user.profile}`;
    }
    return user;
  }

  async findAllUsers(page: number | null, pageSize: number|null): Promise<{ users: any[]; meta: object }> {
    const pageNumber = Math.max(page ?? 1, 1);
    const offset = (pageNumber - 1) * (pageSize ?? 10);
    const users = await this.userRepo.createQueryBuilder("user")
      .leftJoin("user.user_reviews", "user_reviews")
      .select([
        "user.id as id",
        "user.profile as profile",
        "user.email as email",
        "user.name as name",
        "user.name_arabic as name_arabic",
        "user.designation as designation",
        "user.designation_arabic as designation_arabic",
        "user.department as department",
        "user.department_arabic as department_arabic",
        "user.bio_link as bio_link",
        "user.new_joiner as new_joiner",
        "user.status as status",
        "user.is_cultural_ambassador as is_cultural_ambassador",
        "user.dn as dn",
        "user.gender as gender",
        "user.account_expires as account_expires",
        "user.user_principal_name as user_principal_name",
        "user.role as role",
        "user.activity as activity",
        "user.extension as extension"
        // "user.created_at as createdAt",
        // "user.updated_at as updatedAt",
      ])
      .addSelect([
        `SUM(CASE 
          WHEN EXTRACT(MONTH FROM user_reviews.createdAt) = EXTRACT(MONTH FROM NOW()) 
          AND EXTRACT(YEAR FROM user_reviews.createdAt) = EXTRACT(YEAR FROM NOW()) 
          THEN user_reviews.rating 
        ELSE 0 
        END) AS monthly_rating`,
        `SUM(CASE 
          WHEN EXTRACT(YEAR FROM user_reviews.createdAt) = EXTRACT(YEAR FROM NOW()) 
          THEN user_reviews.rating 
          ELSE 0 
        END) AS yearly_rating`
      ])
      .groupBy("user.id")
      .offset(offset)
      .limit(pageSize)
      .getRawMany();
    
    const total = await this.userRepo.createQueryBuilder("user")
      .select("COUNT(DISTINCT user.id)", "count")
      .getRawOne();
    
    return { 
      users, 
      meta: {
        totalCount: total.count,
        currentPage: page,
        pageSize: pageSize,
        totalPages: Math.ceil(total.count / pageSize)
      }
    };
  }
 
  async findUserByEmail(username: string): Promise<any> {
    try {
      let user: User = await this.userRepo.findOne({
        where: {
          user_principal_name: username,
          // password: password
        },
      });
      console.log('findUserByEmail: ', user);

      return user;
    } catch (error) {
      console.log('error in user.service.ts > ln: 316', error);
      throw Error(error);
    }
  }

  async deleteUser(id: number): Promise<String> {
    await this.userRepo.delete(id);
    return `${id} user has been deleted from the database`;
  }
}
