import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    UploadedFile,
    UseInterceptors,
    UsePipes,
    ValidationPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { CreateNewsInput } from './dto/create-news.dto';
import { UpdateNewsInput } from './dto/update-news.dto';
import { NewsService } from './news.service';

import { plainToClass } from 'class-transformer';

let multerOptions = {
    storage: diskStorage({
        destination: './resource/v1/news',
        filename: (req, featuredImage, callback) => {
            const uniqueSuffix = Date.now() + Math.round(Math.random() * 1e3);
            const ext = extname(featuredImage.originalname);
            const fileName = `${uniqueSuffix}${ext}`.toString();
            callback(null, fileName);
        },
    }),
};

@Controller('v1/our-news')
export class NewsController {
    constructor(private readonly newsService: NewsService) { }

    @Post()
    @UseInterceptors(FileInterceptor('featuredImage', multerOptions))
    @UsePipes(new ValidationPipe({ transform: true }))
    async createNews(
        @UploadedFile() file: Express.Multer.File,
        @Body() createNewsDto: any, // Use any to receive raw form data
    ) {
        try {
            // Transform the form data to proper types
            const transformedDto = this.transformFormData(createNewsDto);
            
            let path = file?.path;
            console.log(path);
            let featuredImage = path?.replace(/resource\/v1[\/\\]/g, '');

            // Await the asynchronous operation
            let data = await this.newsService.createNews({
                ...transformedDto,
                featuredImage,
            });

            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            console.log(error);
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error?.driverError,
            };
            return Response;
        }
    }

    private transformFormData(formData: any): any {
        // Transform date strings to Date objects
        const transformed = { ...formData };
        
        if (transformed.visibility) {
            transformed.visibility = new Date(transformed.visibility);
        }
        if (transformed.publication) {
            transformed.publication = new Date(transformed.publication);
        }
        if (transformed.created_on) {
            transformed.created_on = new Date(transformed.created_on);
        }
        if (transformed.updated_on) {
            transformed.updated_on = new Date(transformed.updated_on);
        }

        return transformed;
    }

    @Get()
    async findAllNews() {
        try {
            let data = await this.newsService.allNews();
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error,
            };
            return Response;
        }
    }

    @Get('/external-news')
    async getExternalNews() {
        try {
            let data = await this.newsService.allExternalNews();
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error,
            };
            return Response;
        }
    }

    @Get('/internal-news')
    async getInternalNews() {
        try {
            let data = await this.newsService.allInternalNews();
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error,
            };
            return Response;
        }
    }

    @Patch(':id')
    @UseInterceptors(FileInterceptor('featuredImage', multerOptions))
    async updateNews(
        @Param('id') id: string,
        @Body() updateNewsInput: any,
        @UploadedFile() file: Express.Multer.File,
    ) {
        try {
            const transformedDto = this.transformFormData(updateNewsInput);
            
            if (file) {
                let path = file?.path;
                var featuredImage = path?.replace(/resource\/v1[\/\\]/g, '');
            }
            
            let data = await this.newsService.update(id, { ...transformedDto, featuredImage } as UpdateNewsInput);
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error?.driverError,
            };
            return Response;
        }
    }

    @Delete(':id')
    async removeNews(@Param('id') id: string) {
        try {
            let data = await this.newsService.remove(id);
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error,
            };
            return Response;
        }
    }
}