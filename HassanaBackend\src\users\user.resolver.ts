import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { UserService } from './user.service';
// import { User } from './entities/user.entity';
import { UserSchema } from './schema/user.schema';
import * as jwt from 'jsonwebtoken';
import { LoginUser } from './dto/login-user';
import { User } from './entities/user.entity';
import { Logger } from '@nestjs/common';
import { redis } from '../../redis';
import { UUID } from 'crypto';
import { UserPaginationSchema } from './schema/usersMeta.schema';
@Resolver(() => UserSchema)
export class UserResolver {
  constructor(private readonly userService: UserService) { }

  @Query(() => UserPaginationSchema, { name: 'users' })
  async getAllUsers(@Args('page') page: number, @Args('pageSize') pageSize: number): Promise<any> {
    try {
      const { users, meta } = await this.userService.findAllUsers(page, pageSize);
      return { users, meta };
    } catch (error) {
      return error;
    };
  }
  
  @Query((returns) => [UserSchema])
  getNewUsers(@Args('days') days: number) {
    return this.userService.findNewUsers(days);
  }

  @Query((returns) => [UserSchema])
  async getCulturalAmbassadors() {
    return await this.userService.findAllCulturalAmbassadors();
  }

  @Query((returns) => LoginUser)
  async loginUser(@Args('username') username: string, @Args('password') password: string,): Promise<object> {
    try {
      let userAuthenticationResult = await this.userService.userAuth(username, password);
      Logger.log("User Authentication: ", userAuthenticationResult);
      if (userAuthenticationResult) {
        let user: User;
        user = await this.userService.findUserByEmail(userAuthenticationResult.userPrincipalName);

        if (!user?.id) {
          Logger.log('User not found...');
          user = await this.userService.createNewUser(userAuthenticationResult);
          Logger.log("Created new user > ");
        }

        await redis.hset(`user: ${user.id}`, 'password', password);
        await redis.hset(`user: ${user.id}`, 'username', username);

        let redisValue = await redis.hgetall(`user: ${user.id}`);
        Logger.log('Get all hashes', redisValue);

        let tokenPayload = {
          id: user.id,
          username: user.name,
          role: user.role,
        };
        const KEY = process.env.JWT_KEY
        const token = jwt.sign(tokenPayload, KEY, {});

        let payload: LoginUser = {
          id: user.id,
          username: user.name,
          role: user.role,
          token: token,
        };

        return payload;
      } else {
        Logger.log('Authentication failed...');
        throw new Error('Authentication failed');
      }
    } catch (error) {
      Logger.error('An error occurred:', error);
      console.log(error);
      throw new Error('Server Error');
    }
  }

  // @Mutation(() => UserSchema)
  // @Mutation((returns) => String)
  // createUser() {
  //   return this.userService.createUser();
  // }

  //   @Query(() => [User], { name: 'users' })
  //   findAll() {
  //     return this.usersService.findAll();
  //   }

  @Query(() => UserSchema)
  findUserById(@Args('id') id: UUID) {
    return this.userService.findUserById(id);
  }

  // @Mutation((returns) => UserSchema)
  // updateUser(@Args('updateUserInput') updateUserInput: UpdateUserInput) {
  //   return this.userService.update(updateUserInput.id, updateUserInput);
  // }

  //   @Mutation(() => User)
  //   removeUser(@Args('id', { type: () => Int }) id: number) {
  //     return this.usersService.remove(id);
  //   }
}
