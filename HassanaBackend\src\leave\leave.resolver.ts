import { Resolver, Query, Mutation, Args, Int, ID } from '@nestjs/graphql';
import { LeaveService } from './leave.service';
import { Leave } from './schema/leave.schema';
import { CreateLeaveInput } from './dto/create-leave.input';
import { UpdateLeaveInput } from './dto/update-leave.input';
import { LeaveCount } from './dto/leaveCount';
import { UUID } from 'crypto';

@Resolver(() => Leave)
export class LeaveResolver {
  constructor(private readonly leaveService: LeaveService) {}

  @Mutation(() => Leave)
  createLeave(@Args('createLeaveInput') createLeaveInput: CreateLeaveInput) {
    return this.leaveService.create(createLeaveInput);
  }

  @Query(() => [Leave], { name: 'leaves' })
  findAll() {
    return this.leaveService.findAll();
  }

  @Query(() => LeaveCount, { name: 'getUserLeaves' })
  getUserLeaves(@Args('id', { type: () => ID }) id: number) {
    return this.leaveService.userLeave(id);
  }
  @Query(() => Leave, { name: 'leave' })
  findOne(@Args('id', { type: () => ID }) id: UUID) {
    return this.leaveService.findOne(id);
  }

  @Mutation(() => Leave)
  updateLeave(@Args('updateLeaveInput') updateLeaveInput: UpdateLeaveInput) {
    return this.leaveService.update(updateLeaveInput.id, updateLeaveInput);
  }

  @Mutation(() => Leave)
  removeLeave(@Args('id', { type: () => ID }) id: UUID) {
    return this.leaveService.remove(id);
  }
}
