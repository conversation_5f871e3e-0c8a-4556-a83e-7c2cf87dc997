import { Body, Controller, Delete, Get, Param, Patch, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { LibraryService } from './library.service';
import { CreateLibraryInput } from './dto/create-library.input';
import { UUID } from 'crypto';

let multerOptions = {
    storage: diskStorage({
        destination(req, file, callback) {
            const fileName = file.originalname;
            if (!file) return callback(Error("Please upload any file"), fileName)
            if (file?.mimetype != "application/pdf") return callback(new Error("Only pdf files are allowed"), fileName)
            callback(null, "./libraryh/v1/lib")
        },
        filename: (req, file, callback) => {
            const fileName = file.originalname;
            callback(null, fileName);
        },
    }),
};

@Controller('v1/library')
export class LibraryController {
    constructor(private readonly libraryService: LibraryService) { }

    @Post()
    @UseInterceptors(FileInterceptor('file', multerOptions))
    async createFile(@UploadedFile() file: Express.Multer.File) {
        try {
            let file_path = file?.path?.replace(/library\/v1[\/\\]/g, '');
            console.log(file_path);
            let data = await this.libraryService.create({
                file_name: file.originalname,
                file_path: file_path,
                file_type: file.mimetype
            });

            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            console.log("error : ",error);
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError,
            };
            return Response;
        };
    };

    @Get()
    async findAllLibrary(): Promise<Object> {
        try {
            let data = await this.libraryService.findAll();
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code,
                mesage: error?.message,
                error: error,
            };
            return Response;
        }
    };

    @Delete(':id')
    async removeLibrary(@Param('id') id: UUID) {
        try {
            let data = await this.libraryService.remove(id);
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code,
                mesage: error?.message,
                error: error,
            };
            return Response;
        };
    };
};
