import { Resolver, Query, Mutation, Args, Int, ID } from '@nestjs/graphql';
import { NotificationService } from './notification.service';
import { CreateNotificationInput } from './dto/create-notification.input';
import { UpdateNotificationInput } from './dto/update-notification.input';
import { NotificationSchema } from './schema/notification.schema';
import { NotificationViewEntity } from './entities/notification.entity';
import { UUID } from 'crypto';

@Resolver(() => NotificationSchema)
export class NotificationResolver {
  constructor(private readonly notificationService: NotificationService) { }

  @Mutation(() => NotificationSchema)
  createNotification(@Args('createNotificationInput') createNotificationInput: CreateNotificationInput) {
    return this.notificationService.create(createNotificationInput);
  }
  @Mutation(() => NotificationSchema)
  addNotificationView(@Args('notificationId', { type: () => ID }) notificationId: UUID, @Args('userId', { type: () => ID }) userId: UUID) {
    return this.notificationService.addView(notificationId, userId);
  }

  @Query(() => [NotificationSchema])
  notifications() {
    return this.notificationService.findAll();
  }
  @Query(() => [NotificationSchema])
  notificationViews() {
    return this.notificationService.findAllNotificationViews();
  }

  @Query(() => [NotificationSchema])
  newNotificationsForUser(@Args('id', { type: () => ID }) id: number) {
    return this.notificationService.getAllNewNotificationsForUser(id);
  }

@Query(() => ID)
  unseenNotificationsCount(@Args('userId', { type: () => ID }) userId: UUID) {
    return this.notificationService.getUnseenNotificationsCount(userId);
  }

  @Mutation(() => Boolean)
  markAllNotificationsAsSeen(@Args('userId', { type: () => ID }) userId: UUID) {
    return this.notificationService.markAllNotificationsAsSeen(userId);
  }
  
 // @Query(() => [NotificationViewEntity],)
  //notificationsViewer() {
  //  return this.notificationService.findAllNotificationViews();
  //}

  @Query(() => NotificationSchema,)
  notification(@Args('id', { type: () => ID }) id: UUID) {
    return this.notificationService.findOne(id);
  }

  @Mutation(() => NotificationSchema)
  updateNotification(
    @Args('id', { type: () => ID }) id: UUID,
    @Args('updateNotificationInput') updateNotificationInput: UpdateNotificationInput
  ) {
    return this.notificationService.update(id, updateNotificationInput);
  }



  @Mutation(() => NotificationSchema)
  removeNotification(@Args('id', { type: () => ID }) id: UUID) {
    return this.notificationService.remove(id);
  }
}
