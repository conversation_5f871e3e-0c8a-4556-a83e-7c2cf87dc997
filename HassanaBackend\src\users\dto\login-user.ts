import { InputType, Int, Field, ObjectType } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { UUID } from 'crypto';
import { IntegerType } from 'typeorm';

@ObjectType()
export class LoginUser {

  @ApiProperty()
  @Field()
  id: UUID

  @ApiProperty()
  @Field()
  username: string;

  @ApiProperty()
  @Field()
  role: string;

  @ApiProperty()
  @Field()
  token: string;
}
