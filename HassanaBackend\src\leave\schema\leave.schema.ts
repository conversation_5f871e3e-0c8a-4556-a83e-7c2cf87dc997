import { Field, Int, ObjectType, ID } from "@nestjs/graphql";
import { UUID } from "crypto";

@ObjectType()
export class Leave {
    @Field((type) => ID)
    id: UUID;

    @Field((type) => ID)
    userid: UUID;

    @Field()
    username: string;
    
    @Field()
    remarks: string;

    @Field()
    date: Date;

    @Field((type) => Int)
    numberOfDays: number;

    @Field()
    typeOfLeave: string;

    // @Field()
    // status: string;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;
}