import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsU<PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { UUID } from "crypto";

export class CreateUserReviewDto {

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  @Min(1, { message: "Rating must be at least 1" })
  @Max(1, { message: "Rating must not exceed 1" })
  rating: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  review: string | null;

  @ApiProperty()
  @IsUUID()
  user_id: UUID;

}