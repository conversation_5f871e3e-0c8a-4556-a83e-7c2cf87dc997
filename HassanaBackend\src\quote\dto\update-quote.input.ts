import { InputType, PartialType } from '@nestjs/graphql';
import { CreateQuoteInput } from './create-quote.input';
import { IsString, IsBoolean, IsOptional } from 'class-validator';

@InputType()
export class UpdateQuoteInput extends PartialType(CreateQuoteInput) {
  @IsString()
  @IsOptional()
  quote?: string;

  @IsString()
  @IsOptional()
  author?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @IsString()
  @IsOptional()
  visibilityStart?: string;

  @IsString()
  @IsOptional()
  visibilityEnd?: string;
}