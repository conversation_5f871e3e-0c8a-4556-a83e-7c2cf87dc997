import { BaseEntity } from "@app/BaseEntity";
import { User } from "@app/users/entities/user.entity";
import { Field, Int } from "@nestjs/graphql";
import { UUID } from "crypto";
import { Column, Entity, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from "typeorm";

export enum FeedbackType {
  COMPLAINT = "complaint",
  SUGGESTION = "suggestion",
}

@Entity()
export class UserFeedback extends BaseEntity {

  @Field()
  @Column({
    type: "enum",
    default: FeedbackType.SUGGESTION,
    enum: FeedbackType
  })
  type: FeedbackType;

  @Field()
  @Column()
  subject: string;

  @Field()
  @Column()
  description: string

  @Field()
  @Column()
  @ManyToOne(() => User)
  @JoinColumn({name: "user_id"})
  user_id: UUID
}
