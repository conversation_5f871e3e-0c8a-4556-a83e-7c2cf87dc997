import { Field, InputType, Int,  } from '@nestjs/graphql';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { UUID } from 'crypto';

@InputType()
export class CreateOffersInput {
  // @Field()
  //id: UUID;

  @Field()
  @IsNotEmpty()
  name: string;

  @Field()
  @IsOptional()
  contact_information?: string;

  @Field()
  @IsOptional()
  code: string;

  @Field()
  @IsNotEmpty()
  expiry_date: Date;

  @Field({ nullable: true })
  @IsOptional()
  description: string;

  @Field({ nullable: true })
  @IsOptional()
  status?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  created_by?: UUID;

  @Field({ nullable: true })
  @IsOptional()
  updated_by?: UUID;
}
