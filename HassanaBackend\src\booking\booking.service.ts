import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { validateOrReject, Validator } from 'class-validator';
import { Repository } from 'typeorm';
import { CreateBookingInput } from './dto/create-booking.input';
import { UpdateBookingInput } from './dto/update-booking.input';
import { Booking } from './entities/booking.entity';
import { ApprovedBookingInput } from './dto/approved-booking.input';
import { Resource } from '@app/resource/entities/resource.entity';
import * as path from 'path';
import { existsSync, unlinkSync } from 'fs';
import { sendMailToItTechnician, sendMailToParking } from '@app/Emailer';
import { User } from '@app/users/entities/user.entity';
import { UUID } from 'crypto';

@Injectable()
export class BookingService {
  constructor(
    @InjectRepository(Booking)
    private bookingRepository: Repository<Booking>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    // @InjectRepository(Resource)
    // private resourceRepository: Repository<Resource>,
  ) { }

  async findAll(): Promise<Booking[]> {
    try {
      const bookingList = await this.bookingRepository.find();
      if (bookingList && bookingList.length > 0) {
        bookingList.forEach(booking => {
          if (booking.registrationDoc) {
            booking.registrationDoc = `${process.env.SERVER_URL}/${booking.registrationDoc}`;
          }
        })
      }
      return bookingList;
    } catch (error) {
      console.error('Error: ', error);
      throw error;
    }
  }

  async getAllBookingTeaBoy(): Promise<Booking[]> {
    try {
      const bookingList = await this.bookingRepository.find({
        where: { teaBoy: "true" }
      });
      if (bookingList && bookingList.length > 0) {
        bookingList.forEach(booking => {
          if (booking.registrationDoc) {
            booking.registrationDoc = `${process.env.SERVER_URL}/${booking.registrationDoc}`;
          }
        })
      }
      return bookingList;

    } catch (error) {
      console.error('Error: ', error);
      throw error;
    }
  }

  async findByUser(id: UUID): Promise<Booking[]> {
    try {
      const bookingList = await this.bookingRepository.find({
        where: { userId: id }
      });
      if (bookingList && bookingList.length > 0) {
        bookingList.forEach(booking => {
          if (booking.registrationDoc) {
            booking.registrationDoc = `${process.env.SERVER_URL}/${booking.registrationDoc}`;
          }
        })
      }
      return bookingList;
    } catch (error) {
      console.error('Error: ', error);
      throw error;
    }
  }

  async create(updateBookingInput: UpdateBookingInput): Promise<Booking> {
    try {
      let existingBooking = null;
      existingBooking = await this.bookingRepository.findOne({
        where: { uid: updateBookingInput.uid }
      });
      const user = await this.userRepository.findOne({
        where: { id: updateBookingInput.userId }
      });

      if (existingBooking) {
        // Update the existing booking where both id and uid match
        // console.log("image checking for delete", updateBookingInput.registrationDoc);
        if (updateBookingInput.registrationDoc != undefined) {
          const imageInServer = path.join(__dirname, "../../resource/" + existingBooking.registrationDoc);
          if (existsSync(imageInServer)) {
            unlinkSync(imageInServer);
          }
        }

        this.bookingRepository.merge(existingBooking, updateBookingInput);
        existingBooking.updated_on = new Date();

        let updateData = await this.bookingRepository.save(existingBooking);
        if (updateData) {
          updateData.registrationDoc = updateData.registrationDoc ? `${process.env.SERVER_URL}/${updateData.registrationDoc}` : null;
        }
        if (existingBooking.parking == "false" && updateBookingInput.parking == 'true') {
          sendMailToParking({ ...updateBookingInput, userName: user.name });
        }
        if (existingBooking.itTechnician == "false" && updateBookingInput.itTechnician == 'true') {
          sendMailToItTechnician({ ...updateBookingInput, userName: user.name });
        }
        return updateData;
      } else {
        const res = await this.bookingRepository.save(updateBookingInput);
        if (updateBookingInput.parking == 'true') {
          sendMailToParking({ ...updateBookingInput, userName: user.name });
        }
        if (updateBookingInput.itTechnician == 'true') {
          sendMailToItTechnician({ ...updateBookingInput, userName: user.name });
        }
        res.registrationDoc = res.registrationDoc ? `${process.env.SERVER_URL}/${res.registrationDoc}` : null;
        return res;
      }

    } catch (error) {
      throw new Error(`Failed to create or update booking: ${error.message}`);
    }
  }
}