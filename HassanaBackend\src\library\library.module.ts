import { Module } from '@nestjs/common';
import { LibraryService } from './library.service';
// import { LibraryResolver } from './library.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Library } from './entities/library.entity';
import { LibraryController } from './library.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Library])],
  providers: [LibraryService],
  controllers: [LibraryController]
})
export class LibraryModule {}
