import { Injectable } from '@nestjs/common';
import * as httpntlm from 'httpntlm';
import { Redis } from 'ioredis';
import * as xml2js from 'xml2js';
import { UpdateExchangeInfoDto } from './dto/update-exchange-info.dto';

const redis = new Redis({
  port: 6379,
  host: '127.0.0.1',
  password: 'u7xdyOU4YNO16u',
});

@Injectable()
export class ExchangeInfoService {
  // create(createExchangeInfoDto: CreateExchangeInfoDto) {
  //   return 'This action adds a new exchangeInfo';
  // }

  findAll() {
    return `This action returns all exchangeInfo`;
  }

  async findOne(id: number) {
    let redisValue = await redis.hgetall(`user: ${id}`);
    console.log(redisValue);
    return await this.fetchDataForUser(
      redisValue.username,
      redisValue.password,
    );
    // return `This action returns a #${id} exchangeInfo`;
  }

  update(id: number, updateExchangeInfoDto: UpdateExchangeInfoDto) {
    return `This action updates a #${id} exchangeInfo`;
  }

  remove(id: number) {
    return `This action removes a #${id} exchangeInfo`;
  }

  private url: string = 'https://***********/EWS/Exchange.asmx'; // Exchange server URL

  async fetchDataForUser(username: string, password: string): Promise<any> {
    const options = {
      url: this.url,
      username: username,
      password: password,
      // domain: 'HICUAT',
      body: this.createRequestBody(),
      headers: {
        'Content-Type': 'text/xml; charset=utf-8',
        SOAPAction:
          'http://schemas.microsoft.com/exchange/services/2006/messages/FindItem',
      },
      rejectUnauthorized: false,
    };

    return new Promise((resolve, reject) => {
      try {
        httpntlm.post(options, (err, res) => {
          if (err) {
            console.error('Error:', err);
            return reject(err);
          }
          // console.log(res);

          this.parseXMLResponse(res.body).then((result) => {
            console.log(JSON.stringify(result));

            // Extract the specific JSON data
            const errorCheck =
              result['s:Envelope']['s:Body']['m:FindItemResponse'][
                'm:ResponseMessages'
              ]['m:FindItemResponseMessage']['m:ResponseCode'];
            console.log('ErrorCheck' + errorCheck);

            let data = [];
            if (errorCheck == 'NoError') {
              const jsonData =
                result['s:Envelope']['s:Body']['m:FindItemResponse'][
                  'm:ResponseMessages'
                ]['m:FindItemResponseMessage']['m:RootFolder']['t:Items'][
                  't:CalendarItem'
                ];

              console.log("JsonData: ", JSON.stringify(jsonData));
              jsonData.map((item: any) => {
                const returnObject = {
                  title: item['t:Subject'],
                  status: item['t:LegacyFreeBusyStatus'],
                  desccription: item['t:Body'],
                  t_start: item['t:Start'],
                  t_end: item['t:End'],		                  t_uid: item['t:UID'],
                  item_id: item['t:UID']
                };
                data.push(returnObject);
              });

              resolve(data);
            }
            else {
              resolve(data);
            }
          });
        });
      } catch (error) {
        reject(error);
        console.log(error);
      }
    });
  }

  private createRequestBody(): string {
    // Modify this XML body as per your requirements
    return `<?xml version="1.0" encoding="utf-8"?>
      <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xmlns:m="http://schemas.microsoft.com/exchange/services/2006/messages"
                     xmlns:t="http://schemas.microsoft.com/exchange/services/2006/types"
                     xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
          <soap:Header>
              <t:RequestServerVersion Version="Exchange2010" />
          </soap:Header>
          <soap:Body>
              <m:FindItem Traversal="Shallow">
                  <m:ItemShape>
                      <t:BaseShape>AllProperties</t:BaseShape>
                      <t:AdditionalProperties>
                          <t:FieldURI FieldURI="item:Subject" />
                          <t:FieldURI FieldURI="item:Body" />
                          <t:FieldURI FieldURI="calendar:LegacyFreeBusyStatus" />
                          <t:FieldURI FieldURI="calendar:Start" />
                          <t:FieldURI FieldURI="calendar:End" />
                          <t:FieldURI FieldURI="calendar:UID" />
                      </t:AdditionalProperties>
                  </m:ItemShape>
                  <m:CalendarView MaxEntriesReturned="50"
                                  StartDate="2023-01-01T00:00:00Z"
                                  EndDate="2023-12-31T23:59:59Z" />
                  <m:ParentFolderIds>
                      <t:DistinguishedFolderId Id="calendar" />
                  </m:ParentFolderIds>
              </m:FindItem>
          </soap:Body>
      </soap:Envelope>`;
  }

  private parseXMLResponse(xmlBody: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const parser = new xml2js.Parser({
        explicitArray: false,
        ignoreAttrs: true,
      });
      parser.parseString(xmlBody, (err, result) => {
        if (err) {
          console.error('Error parsing XML:', err);
          return reject(err);
        }
        resolve(result);
      });
    });
  }
}
