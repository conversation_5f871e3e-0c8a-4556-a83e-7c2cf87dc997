
import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { UUID } from 'crypto';

@ObjectType()
export class QuoteSchema {

    @Field(() => ID, {nullable: true})
    id: UUID;

    @Field()
    quote: string;

    @Field()
    author: string;

    @Field()
    status: Boolean;

    @Field()
    visibilityStart: String;

    @Field()
    visibilityEnd: String;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;

}
