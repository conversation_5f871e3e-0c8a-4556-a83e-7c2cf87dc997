// import { ObjectType, Field, Int } from '@nestjs/graphql';
import { BaseEntity } from '@app/BaseEntity';
import { User } from '@app/users/entities/user.entity';
import { UUID } from 'crypto';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Unique } from 'typeorm';


@Entity({ name: "offers" })
export class Offers extends BaseEntity {

    @Column()
    name: string;

    @Column({ nullable: true })
    contact_information?: string;

    @Column()
    code: string;

    @Column()
    expiry_date?: Date;

    @Column({ nullable: true })
    description: string;

    @Column()
    status: boolean;

    @Column({ nullable: true })
    created_by: UUID;

    @Column({ nullable: true })
    updated_by?: UUID;
}

@Entity({ name: "offers_view" })
@Unique(["user_id", "offer_id"])
export class OffersViewEntity extends BaseEntity {

    @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
    @JoinColumn({ name: "user_id" })
    user_id: UUID;

    @ManyToOne(() => Offers, (offers) => offers.id, { onDelete: 'CASCADE' })
    @JoinColumn({ name: "offer_id" })
    offer_id: UUID;

}
