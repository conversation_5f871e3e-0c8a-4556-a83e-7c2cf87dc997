import { Injectable } from '@nestjs/common';
import { CreateLeaveInput } from './dto/create-leave.input';
import { UpdateLeaveInput } from './dto/update-leave.input';
import { InjectRepository } from '@nestjs/typeorm';
import { Leave } from './entities/leave.entity';
import { Between, Equal, Repository } from 'typeorm';
import { UUID } from 'crypto';

@Injectable()
export class LeaveService {

  constructor(
    @InjectRepository(Leave)
    private readonly leaveRepository: Repository<Leave>,
  ) { }

  async create(createLeaveInput: CreateLeaveInput): Promise<Leave> {
    const newLeave = this.leaveRepository.create({
      ...createLeaveInput
    });

    const savedLeave = await this.leaveRepository.save(newLeave);

    return savedLeave;
  }

  findAll(): Promise<Leave[]> {
    return this.leaveRepository.find();
  }

  findOne(id: UUID): Promise<Leave> {

    return this.leaveRepository.findOne({ where: { id } });
  }

  // async userLeave(id: number): Promise<Leave[]> {
  async userLeave(id: number): Promise<{ medical: number, casual: number }> {
    try {
      const currentYear = new Date().getFullYear();
      const startDate = `${currentYear}-01-01`;
      const endDate = `${currentYear}-12-31`;
    
      // Sum of medical leaves
      const medicalCount = await this.leaveRepository
        .createQueryBuilder()
        .select('SUM("Leave"."numberOfDays")', 'totalMedicalDays')
        .where('"Leave"."userId" = :id', { id })
        .andWhere('"Leave"."date" BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere('"Leave"."typeOfLeave" = :type', { type: 'medical' })
        .getRawOne();
    
      // Sum of casual leaves
      const casualCount = await this.leaveRepository
        .createQueryBuilder()
        .select('SUM("Leave"."numberOfDays")', 'totalCasualDays')
        .where('"Leave"."userId" = :id', { id })
        .andWhere('"Leave"."date" BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere('"Leave"."typeOfLeave" = :type', { type: 'casual' })
        .getRawOne();
    
      const totalMedicalDays = medicalCount.totalMedicalDays || 0;
      const totalCasualDays = casualCount.totalCasualDays || 0;
    
      return { medical: totalMedicalDays, casual: totalCasualDays };
    } catch (error) {
      console.error('Error fetching leave counts for user:', error);
      // Handle the error appropriately
      // You might throw a custom error or handle it based on your application's needs
    }

    
    // try {
    //   const currentYear = new Date().getFullYear();
    //   const startDate = new Date(`${currentYear}-01-01`);
    //   const endDate = new Date(`${currentYear}-12-31`);

    //   // Count for medical leaves
    //   const medicalCount = await this.leaveRepository.count({
    //     where: {
    //       userId: Equal(id),
    //       date: Between(startDate, endDate),
    //       typeOfLeave: Equal('medical') // Adjust if the actual value is different
    //     }
    //   });

    //   // Count for casual leaves
    //   const casualCount = await this.leaveRepository.count({
    //     where: {
    //       userId: Equal(id),
    //       date: Between(startDate, endDate),
    //       typeOfLeave: Equal('casual') // Adjust if the actual value is different
    //     }
    //   });

    //   return { medical: medicalCount, casual: casualCount };

    // } catch (error) {
    //   console.error('Error fetching leave counts for user:', error);
    //   // Handle the error appropriately
    //   // You might throw a custom error or handle it based on your application's needs
    // }
  }


  async update(id: UUID, updateLeaveInput: UpdateLeaveInput) {
    const existingLeave = await this.leaveRepository.findOne({ where: { id } });

    if (existingLeave) {
      this.leaveRepository.merge(existingLeave, updateLeaveInput);
      return this.leaveRepository.save(existingLeave);
    }

    return null;
  }

  async remove(id: UUID): Promise<Leave | null> {
    const leaveToRemove = await this.leaveRepository.findOne({ where: { id } });

    if (leaveToRemove) {
      await this.leaveRepository.remove(leaveToRemove);
      return leaveToRemove;
    }

    return null;
  }
}
