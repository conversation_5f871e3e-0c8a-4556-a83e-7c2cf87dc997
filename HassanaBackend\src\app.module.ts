import { ApolloDriver } from '@nestjs/apollo';
import { Module } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ConfigModule } from '@nestjs/config';
import { MulterModule } from '@nestjs/platform-express';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { AnnouncementModule } from './announcement/announcement.module';
import { AppController } from './app.controller';
import { AppResolver } from './app.resolver';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { BookingModule } from './booking/booking.module';
import { EventModule } from './event/event.module';
// import { NewsModule } from './news/news.module';
import { ResourceModule } from './resource/resource.module';
import { TestModule } from './test/test.module';
import { UserModule } from './users/user.module';
import { QuoteModule } from './quote/quote.module';
import { NotificationModule } from './notification/notification.module';
import { NewsModule } from './news/news.module';
import { ExchangeInfoModule } from './exchange-info/exchange-info.module';
import { LeaveModule } from './leave/leave.module';
import { FileUploadModule } from './file-upload/file-upload.module';
import { LibraryModule } from './library/library.module';
import { OfferModule } from './offers/offers.module';
import { UserFeedbackModule } from './user-feedback/user-feedback.module';
import { UserReviewsModule } from './user-reviews/user-reviews.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,  // Makes config available globally without needing to import it in every module
      envFilePath: `.env`,  // Automatically loads the correct .env file
    }),
    GraphQLModule.forRoot({
      driver: ApolloDriver,
      playground: true,
      introspection: true,
      autoSchemaFile: join(
        process.cwd(),
        'src/auto-generated-schema/schema.graphql',
      ),
      definitions: {
        path: join(process.cwd(), 'src/auto-generated-schema/graphql.ts'),
      },
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      useUTC: true,
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: true,
      logging: true
    }),

    MulterModule.register(),
    TestModule,
    UserModule,
    AuthModule,
    NewsModule,
    BookingModule,
    ResourceModule,
    AnnouncementModule,
    EventModule,
    QuoteModule,
    NotificationModule,
    ExchangeInfoModule,
    LeaveModule,
    FileUploadModule,
    LibraryModule,
    OfferModule,
    UserFeedbackModule,
    UserReviewsModule
    // Test2Module,
    // CategoryModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})

export class AppModule {}
