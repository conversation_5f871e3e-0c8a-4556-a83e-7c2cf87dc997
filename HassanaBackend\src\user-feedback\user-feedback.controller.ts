import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Res, UseGuards, Req } from '@nestjs/common';
import { UserFeedbackService } from './user-feedback.service';
import { CreateUserFeedbackDto } from './dto/create-user-feedback.dto';
import { UpdateUserFeedbackDto } from './dto/update-user-feedback.dto';
import { UUID } from 'crypto';
import { JwtGuard } from '@app/auth/jwt.guard';
import { ApiBody } from '@nestjs/swagger';


@Controller('v1/user-feedback')
export class UserFeedbackController {
  constructor(private readonly userFeedbackService: UserFeedbackService) {}

   @UseGuards(JwtGuard)
  @Post()
  @ApiBody({ type: CreateUserFeedbackDto })
  async create(@Body() createUserFeedbackDto: CreateUserFeedbackDto, @Req() req: Request) {
    try {
      //const id = "aa676b05-c800-4b66-aebe-84cde30d7f64";
      const { id } = req['user'];
      console.log(id);
      
      	
      let data = await this.userFeedbackService.create(createUserFeedbackDto, id);
      return {
        status: true,
        message: "Success",
        data: data
      };
    } catch (error) {
      return {
        status: false,
        message: "Internal Server Error",
        errorMessage: error.message
      };
    };
  }

  @Get()
  async findAll() {
    try {
      let data = await this.userFeedbackService.findAll();
      return {
        status: true,
        message: "Success",
        data: data
      }
    } catch (error) {
      return {
        status: false,
        message: "Internal Server Error",
        errorMessage: error.message
      }
    }
  }

  @Delete(':id')
  async remove(@Param('id') id: UUID) {
    try {
      let data = await this.userFeedbackService.remove(id);
      return {
        status: true,
        message: "Success",
        data: data
      }
    } catch (error) {
      return {
        status: false,
        message: "Internal Server Error",
        errorMessage: error.message
      }
    }
  }
}
