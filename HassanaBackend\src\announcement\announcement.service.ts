import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { existsSync, unlinkSync } from 'fs';
import * as path from 'path';
import { Repository } from 'typeorm';
import { CreateAnnouncementInput } from './dto/create-announcement.input';
import { UpdateAnnouncementInput } from './dto/update-announcement.input';
import { Announcement, AnnouncementViewEntity } from './entities/announcement.entity';
import { UUID } from 'crypto';

@Injectable()
export class AnnouncementService {

    constructor(
        @InjectRepository(Announcement)
        private readonly announcementRepository: Repository<Announcement>,
        @InjectRepository(AnnouncementViewEntity)
        private readonly announcementViewRepository: Repository<AnnouncementViewEntity>
    ) { }

    async create(createAnnouncementInput: CreateAnnouncementInput): Promise<Announcement> {
        const newAnnouncement = this.announcementRepository.create({
            ...createAnnouncementInput
        });

        const savedAnnouncement = await this.announcementRepository.save(newAnnouncement);
        savedAnnouncement.image ? savedAnnouncement.image = `${process.env.SERVER_URL}/${savedAnnouncement.image}` : null;
        return savedAnnouncement;
    }

    async findAll(userId: UUID): Promise<Announcement[]> {
        const announcementList = await this.announcementRepository.createQueryBuilder("announcement")
            .leftJoinAndSelect("announcement_view", "view", "view.announcement_id = announcement.id AND view.user_id = :userId", { userId })
            .select([
                "announcement.id AS id",
                "announcement.title AS title",
                "announcement.details AS details",
                "announcement.category AS category",
                "announcement.status AS status",
                "announcement.visibility AS visibility",
                "announcement.image AS image",
                "CASE WHEN view.id IS NOT NULL THEN true ELSE false END AS is_read"
            ])
            .orderBy("announcement.createdAt", "DESC")
            .getRawMany();

        if (announcementList && announcementList.length > 0) {
            announcementList.forEach(announcement => {
                announcement.image ? announcement.image = `${process.env.SERVER_URL}/${announcement.image}` : null;
            });
        }
        return announcementList;
    };

    async findOne(id: UUID): Promise<Announcement> {
        let announcement = await this.announcementRepository.findOne({ where: { id } });
        if (!announcement) throw new NotFoundException('Announcement not found');
        announcement.image ? announcement.image = `${process.env.SERVER_URL}/${announcement.image}` : null;
        return announcement;
    }

    async update(id: UUID, updateAnnouncementInput: UpdateAnnouncementInput): Promise<UpdateAnnouncementInput> {
        const existingAnnouncement = await this.announcementRepository.findOne({ where: { id } });
        if (!existingAnnouncement) throw new NotFoundException('Announcement not found');

        if (updateAnnouncementInput.image) {
            const imageInServer = path?.join(__dirname, "../../resource/" + existingAnnouncement.image);
            if (existsSync(imageInServer)) {
                console.log("deleting Image in server ...: " + imageInServer);
                unlinkSync(imageInServer);
            };
        };
        this.announcementRepository.merge(existingAnnouncement, updateAnnouncementInput);
        // existingAnnouncement.updated_on = new Date();
        return this.announcementRepository.save(existingAnnouncement);
    }

    async remove(id: UUID): Promise<Announcement | null> {
        const announcementToRemove = await this.announcementRepository.findOne({ where: { id } });

        if (!announcementToRemove) throw new NotFoundException('Announcement not found');

        if (announcementToRemove) {
            try {
                const imagePath = path?.join(__dirname, "../../resource/" + announcementToRemove.image);
                if (existsSync(imagePath)) {
                    console.log("deleting Image in server ...: " + imagePath);
                    unlinkSync(imagePath);
                };

                await this.announcementRepository.remove(announcementToRemove);
                return announcementToRemove;

            } catch (error) {
                throw error;
            }
        }
        return null;
    }

    async createAnnouncementView(announcementId: UUID, userId: UUID) {
        await this.announcementViewRepository.upsert({
            announcement_id: announcementId,
            user_id: userId
        }, { conflictPaths: ["announcement_id", "user_id"]})
    }
}