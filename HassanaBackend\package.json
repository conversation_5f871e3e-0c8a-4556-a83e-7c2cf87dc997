{"name": "nest-boilerplate", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@apollo/gateway": "^2.5.6", "@apollo/subgraph": "^2.5.6", "@nestjs/apollo": "^12.0.9", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/graphql": "^12.0.9", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^8.1.1", "@nestjs/typeorm": "^10.0.0", "@types/activedirectory2": "^1.2.6", "@types/ldapjs": "^3.0.5", "@types/multer": "^1.4.11", "activedirectory2": "^2.2.0", "apollo-server-express": "^3.12.1", "axios": "^1.6.0", "axios-ntlm": "^1.4.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "ews-javascript-api": "^0.13.2", "fs": "^0.0.1-security", "graphql": "^16.8.1", "graphql-tools": "^9.0.0", "handlebars": "^4.7.8", "httpntlm": "^1.8.13", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "ldapjs": "^3.0.6", "mime-types": "^3.0.1", "nest": "^0.1.6", "nodemailer": "^6.9.8", "nodemailer-smtp-transport": "^2.7.4", "pg": "^8.11.3", "redis": "^4.6.13", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "ts-morph": "^19.0.0", "typeorm": "^0.3.17", "xml2js": "^0.6.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}