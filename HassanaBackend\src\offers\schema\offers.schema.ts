import { Field, GraphQLISODateTime, Int, ObjectType } from '@nestjs/graphql';
import { UUID } from 'crypto';

@ObjectType()
export class OffersSchema {

   @Field({nullable: true})
    id: UUID;

    @Field()
    name: string;

    @Field({ nullable: true })
    contact_information?: string;

    @Field()
    code: string;

    @Field((type) => GraphQLISODateTime, { nullable: true })
    expiry_date?: Date;

    @Field({ nullable: true })
    description?: string;

    @Field({ nullable: true })
    status?: boolean;

    @Field({ nullable: true })
    is_read?: boolean;

    @Field((type) => GraphQLISODateTime, { nullable: true })
    createdAt: Date;

    @Field({ nullable: true })
    created_by?: string;

    @Field((type) => GraphQLISODateTime, { nullable: true })
    updatedAt?: Date;

    @Field({ nullable: true })
    updated_by?: string;
}
