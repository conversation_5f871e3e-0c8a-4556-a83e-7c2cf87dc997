import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsOptional, IsDateString, IsUUID, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';

@InputType()
export class CreateNewsInput {
  @Field()
  @IsNotEmpty()
  @IsString()
  title: string;

  @Field()
  @IsNotEmpty()
  @IsString()
  url: string;

  @Field()
  @IsNotEmpty()
  @IsString()
  category: string;

  @Field()
  @IsNotEmpty()
  @IsString()
  summary: string;

  @Field()
  @IsNotEmpty()
  @IsString()
  author: string;

  @Field()
  @IsNotEmpty()
  @IsString()
  source: string;

  @Field()
  @IsNotEmpty()
  @IsString()
  status: string;

  @Field()
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  visibility: Date;

  @Field()
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  publication: Date;

  @Field()
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  created_on: Date;

  @Field()
  @IsNotEmpty()
  @IsString()
  created_by: string;

  @Field()
  @IsDateString()
  @Transform(({ value }) => new Date(value))
  updated_on: Date;

  @Field()
  @IsNotEmpty()
  @IsString()
  updated_by: string;

  // This will be set by the controller after file upload
  featuredImage?: string;
}