import { ObjectType, Field, Int } from '@nestjs/graphql';
import { BookingSchema } from '@app/booking/schema/booking.schema';
import { UUID } from 'crypto';
import { UserReview } from '@app/user-reviews/entities/user-review.entity';

@ObjectType()
export class UserSchema {
  @Field()
  id: UUID;

  @Field({ nullable: true })
  profile: string;

  @Field({ nullable: true })
  email: string;

  @Field({ nullable: true })
  name: string;

  @Field({ nullable: true })
  name_arabic: string;

  @Field({ nullable: true })
  designation: string;

  @Field({ nullable: true })
  designation_arabic: string;

  @Field({ nullable: true })
  department: string;

  @Field({ nullable: true })
  department_arabic: string;

  @Field({ nullable: true })
  bio_link: string;

  @Field({ nullable: true })
  new_joiner: string;

  @Field({ nullable: false })
  status: string;

  @Field({ nullable: true })
  is_cultural_ambassador: string;

  @Field({ nullable: true })
  dn: string;

  @Field({ nullable: true })
  gender: string;

  @Field({ nullable: true })
  account_expires: string;

  @Field({ nullable: true })
  user_principal_name: string;

  @Field()
  role: string;

  @Field()
  monthly_rating: number;

  @Field()
  yearly_rating: number;

  // @Field(() => [UserReview])
  // reviews: UserReview[];

  // @Field(() => [BookingSchema], { nullable: true })
  // bookings: BookingSchema[];

  @Field({nullable: true})
  createdAt: Date;

  @Field({ nullable: true })
  updatedAt: Date;

  @Field({nullable: true})
  activity: string;

  @Field({nullable: true})
  extesion: string;
}
