import { ObjectType, Field, Int } from '@nestjs/graphql';
import { UserSchema } from './user.schema';

@ObjectType()
export class UserMeta {
    @Field(() => Int)
    totalCount: number;

    @Field(() => Int)
    currentPage: number;

    @Field(() => Int)
    pageSize: number;

    @Field(() => Int)
    totalPages: number;

}

@ObjectType()
export class UserPaginationSchema {
    @Field(() => [UserSchema])
    users: UserSchema[];

    @Field(() => UserMeta)
    meta: UserMeta;
}
