import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, ExecutionContext } from '@nestjs/common';
import { UserReviewsService } from './user-reviews.service';
import { CreateUserReviewDto } from './dto/create-user-review.dto';
import { UpdateUserReviewDto } from './dto/update-user-review.dto';
import { JwtGuard } from '@app/auth/jwt.guard';
import { ApiBody } from '@nestjs/swagger';

@UseGuards(JwtGuard)
@Controller('v1/user-reviews')
export class UserReviewsController {
  constructor(private readonly userReviewsService: UserReviewsService) {}
  
  @Post()
  @ApiBody({ type: CreateUserReviewDto })
  async create(@Body() createUserReviewDto: CreateUserReviewDto, @Req() request: Request) {
    try {     
      let { id } = request['user'];      
      let data = await this.userReviewsService.create(createUserReviewDto, id);
      return { 
        status: true,
        message: "Success",
        data: data
      }
    } catch (error) {
      console.log(error);
      return {
        status: false,
        message: "Internal Server Error",
        errorMessage: error.message
      }
    }
  }

  @Get()
  async findAll() {
    try {
      let data = await this.userReviewsService.findAll();
      return {
        status: true,
        message: "Success",
        data: data
      }
    } catch (error) {
      return {
        status: false,
        message: "Internal Server Error",
        errorMessage: error.message
      }
    }
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.userReviewsService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateUserReviewDto: UpdateUserReviewDto) {
    return this.userReviewsService.update(+id, updateUserReviewDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.userReviewsService.remove(+id);
  }
}
