import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {cors: true});
  app.useLogger(new Logger());
  app.useStaticAssets(path.join(__dirname, '../../resource'));
  app.useStaticAssets(path.join(__dirname, '../../library'));
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Automatically strip non-decorated fields
      forbidNonWhitelisted: true, // Throw error for non-decorated fields
      transform: true, // Automatically transform payloads to DTOs
      forbidUnknownValues: false,
      transformOptions: { enableImplicitConversion: true },
    }),
  );
  const config = new DocumentBuilder()
    .setTitle('Hassana APIs Document')
    // .setDescription('The cats API description')
    .setVersion('1.0')
    // .addTag('cats')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('/v1/docs', app, documentFactory);


  // Alternatively, you can enable CORS using Nest.js built-in method
  app.enableCors({
    origin: ['*'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    preflightContinue: true,
    optionsSuccessStatus: 204,
    credentials: true,
  });

  await app.listen(process.env.SERVER_PORT, () => {
    Logger.log("Server is running on port: " + process.env.SERVER_PORT)
  });
}

bootstrap();
