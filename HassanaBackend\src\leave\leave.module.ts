import { Module } from '@nestjs/common';
import { LeaveService } from './leave.service';
import { LeaveResolver } from './leave.resolver';
import { Leave } from './entities/leave.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports:[TypeOrmModule.forFeature([Leave])],
  controllers:[],
  providers: [LeaveResolver, LeaveService],
  exports:[LeaveService]
})
export class LeaveModule {}
