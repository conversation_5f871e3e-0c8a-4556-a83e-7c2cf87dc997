// const nodemailer = require('nodemailer');
// const smtpTransport = require('nodemailer-smtp-transport');
// const fs = require('fs').promises;

// // Configure SMTP transport with new credentials and server details
// // const smtpTransport = nodemailer.createTransport({
// //   host: 'smtps://mail.hassana.com.sa',
// //   port: 587,
// //   auth: {
// //     user: 'hassana',
// //     pass: 'P@ssw0rd'
// //   }
// // });

// const transporter = nodemailer.createTransport(smtpTransport({
//   host: 'mail.hassana.com.sa',
//   port: 25,
//   secure: false,
//   auth: {
//     user: '<EMAIL>',
//     pass: 'P@ssw0rd'
//   },

//   tls: {
//     rejectUnauthorized: false // Accept self-signed certificates
//   }

// }));


// const mailOptions = {
//   from: '<EMAIL>', // Sender's email address
//   to: '<EMAIL>',    // Recipient's email address
//   subject: 'Hello from Nodemailer',
//   text: 'This is a test email from Nodemailer!'
// };
// // const smtpTransport = nodemailer.createTransport(poolConfig);

// transporter.sendMail(mailOptions, (error, info) => {
//   if (error) {
//     return console.error('Error sending email:', error);
//   }
//   console.log('Email sent:', info.response);
// });

// // Close the transport
// transporter.close();


// console.log(smtpTransport.);
// const emailFunction = async (userEmail, verification) => {
//   try {
//     smtpTransport.on('connection', (stream) => {
//       console.log('someone connected!');
//     });
//     // // Read the file and compile the email template
//     // // const data = await fs.readFile(assets/${file}, { encoding: 'utf-8' });
//     // const data = "<h1> hello world </h1>";
//     // const template = handlebars.compile(data);
//     // const htmlToSend = template({ code: verification });

//     // // Setup mail options with the new receiver
//     // const mailOptions = {
//     //   from: '<EMAIL>',
//     //   to: userEmail, // or '<EMAIL>' if you want to send to a fixed receiver
//     //   subject: 'Verify Your Account - NFC Bizz',
//     //   html: htmlToSend
//     // };

//     // // Send email
//     // await smtpTransport.sendMail(mailOptions);
//     // return true;
//   } catch (error) {
//     console.error("Error in email:", error);
//     // return false;
//   }
// };
// emailFunction("<EMAIL>", "<h1> hello world </h1>" )

// console.log(smtpTransport.);
// const emailFunction = async (userEmail, verification) => {
//   try {
//     smtpTransport.on('connection', (stream) => {
//       console.log('someone connected!');
//     });
//     // // Read the file and compile the email template
//     // // const data = await fs.readFile(assets/${file}, { encoding: 'utf-8' });
//     // const data = "<h1> hello world </h1>";
//     // const template = handlebars.compile(data);
//     // const htmlToSend = template({ code: verification });

//     // // Setup mail options with the new receiver
//     // const mailOptions = {
//     //   from: '<EMAIL>',
//     //   to: userEmail, // or '<EMAIL>' if you want to send to a fixed receiver
//     //   subject: 'Verify Your Account - NFC Bizz',
//     //   html: htmlToSend
//     // };

//     // // Send email
//     // await smtpTransport.sendMail(mailOptions);
//     // return true;
//   } catch (error) {
//     console.error("Error in email:", error);
//     // return false;
//   }
// };
// emailFunction("<EMAIL>", "<h1> hello world </h1>" )













const {Redis} = require('ioredis');

const redis = new Redis({
  host: '*********',
  port: parseInt('6379'),
  password: 'foobared',
});

redis.on('error', (err) => {
    console.log('Redis Client Error', err);
});

const test = async () => {
  try {
    redis.set("test", "testing")
    const get = await  redis.get('test')
    console.log(get);
  } catch (error) {
    console.log(error);
  }
   
}

test()

// redis.connect().then(() => {console.log("conected")}).catch((err) => {console.log(err);});

module.exports = {redis};

// Handling errors for specific operations
// redis.on('error', (error) => {
//   console.error('Redis error:', error);
//   // Handle the error as needed, such as retrying the operation or logging it
// });

// module.exports = { redis };
















// const httpntlm = require('httpntlm');

// const soapBody = `<?xml version="1.0" encoding="utf-8"?>
// <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
//                xmlns:m="http://schemas.microsoft.com/exchange/services/2006/messages"
//                xmlns:t="http://schemas.microsoft.com/exchange/services/2006/types"
//                xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
//     <soap:Header>
//         <t:RequestServerVersion Version="Exchange2013" />
//     </soap:Header>
//     <soap:Body>
//         <m:CreateItem MessageDisposition="SendAndSaveCopy">
//             <m:SavedItemFolderId>
//                 <t:DistinguishedFolderId Id="sentitems" />
//             </m:SavedItemFolderId>
//             <m:Items>
//                 <t:Message>
//                     <t:Subject>Test Subject</t:Subject>
//                     <t:Body BodyType="Text">Test Body</t:Body>
//                     <t:ToRecipients>
//                         <t:Mailbox>
//                             <t:EmailAddress><EMAIL></t:EmailAddress>
//                         </t:Mailbox>
//                     </t:ToRecipients>
//                 </t:Message>
//             </m:Items>
//         </m:CreateItem>
//     </soap:Body>
// </soap:Envelope>
// `







// const fetchData = async () => {
//   const ntlmOptions = {
//     url: 'https://***************/EWS/Exchange.asmx',
//     username: '<EMAIL>',
//     password: 'aA1bB2cC3',
//     rejectUnauthorized: false, // Insecure, see below for security notes
//     headers: {
//       // 'www-authenticate': 'Negotiate, NTLM',
//       'Content-Type': 'text/xml; charset=utf-8',
//       SOAPAction:
//         'http://schemas.microsoft.com/exchange/services/2006/messages/CreateItem',
//     },
//     body: soapBody,
//   };

//   await httpntlm.post(ntlmOptions, function (err, res) {
//     if (err) {
//       console.error(err);
//       return;
//     }

//     // console.log("headers: ",res.headers);
//     console.log("body ", res.body);
//     // console.log("res: ", res);
//   });
// }

// fetchData()











