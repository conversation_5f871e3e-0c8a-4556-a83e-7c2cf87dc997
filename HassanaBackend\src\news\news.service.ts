import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { existsSync, unlinkSync } from 'fs';
import * as path from 'path';
import { MoreThanOrEqual, Repository } from 'typeorm';
import { CreateNewsInput } from './dto/create-news.dto';
import { UpdateNewsInput } from './dto/update-news.dto';
import { News as NewsEntity } from './entities/news.entity';

type UUID = `${string}-${string}-${string}-${string}-${string}`;

@Injectable()
export class NewsService {
  constructor(
    @InjectRepository(NewsEntity)
    private readonly newsRepository: Repository<NewsEntity>,
  ) { }

  private validateUUID(id: string): UUID {
    // Basic UUID validation - you might want to use a more robust validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      throw new Error('Invalid UUID format');
    }
    return id as UUID;
  }

  async createNews(createNewsInput: CreateNewsInput): Promise<any> {
    try {
      const res = await this.newsRepository.save(createNewsInput);
      if (res.featuredImage) {
        res.featuredImage = `${process.env.SERVER_URL}/${res.featuredImage}`;
      }
      return res;
    } catch (error) {
      throw error;
    }
  }

  async allNews(): Promise<NewsEntity[]> {
    const newsList = await this.newsRepository.find();

    if (newsList && newsList.length > 0) {
      newsList.forEach(news => {
        if (news.featuredImage) {
          news.featuredImage = `${process.env.SERVER_URL}/${news.featuredImage}`;
        }
        console.log(news.featuredImage);
      });
    }

    return newsList;
  }

  async allExternalNews(): Promise<NewsEntity[]> {
    const todayStart = new Date();
  todayStart.setUTCHours(0, 0, 0, 0); 
    const newsList = await this.newsRepository.find({ 
      where: { 
        status: "true", 
        category: "external", 
        visibility: MoreThanOrEqual(todayStart) 
      } 
    });

    if (newsList && newsList.length > 0) {
      newsList.forEach(news => {
        if (news.featuredImage) {
          news.featuredImage = `${process.env.SERVER_URL}/${news.featuredImage}`;
        }
      });
    }

    return newsList;
  }
  
  async allInternalNews(): Promise<NewsEntity[]> {
    const todayStart = new Date();
  todayStart.setUTCHours(0, 0, 0, 0);  // Start of today in UTC
    const newsList = await this.newsRepository.find({ 
      where: { 
        status: "true", 
        category: "internal", 
        visibility: MoreThanOrEqual(todayStart) 
      } 
    });

    if (newsList && newsList.length > 0) {
      newsList.forEach(news => {
        if (news.featuredImage) {
          news.featuredImage = `${process.env.SERVER_URL}/${news.featuredImage}`;
        }
      });
    }

    return newsList;
  }

  async findOne(id: string): Promise<NewsEntity> {
    const validId = this.validateUUID(id);
    const news = await this.newsRepository.findOne({ where: { id: validId } });
    if (news && news.featuredImage) {
      news.featuredImage = `${process.env.SERVER_URL}/${news.featuredImage}`;
    }
    return news;
  }

  async update(id: string, updateNewsInput: UpdateNewsInput): Promise<NewsEntity> {
    try {
      const validId = this.validateUUID(id);
      const existingNews = await this.newsRepository.findOne({ where: { id: validId } });
      if (existingNews) {
        console.log("image checking for delete", updateNewsInput.featuredImage);

        if (updateNewsInput.featuredImage != undefined) {
          const imageInServer = path.join(__dirname, "../../resource/" + existingNews.featuredImage);
          if (existsSync(imageInServer)) {
            console.log("deleting Image in server ...: " + imageInServer);
            unlinkSync(imageInServer);
          }
        }

        this.newsRepository.merge(existingNews, updateNewsInput);
        existingNews.updated_on = new Date();

        let updateData = await this.newsRepository.save(existingNews);
        if (updateData && updateData.featuredImage) {
          updateData.featuredImage = `${process.env.SERVER_URL}/${updateData.featuredImage}`;
        }

        return updateData;
      }
    } catch (error) {
      throw error;
    }
  }

  async remove(id: string): Promise<NewsEntity | null> {
    const validId = this.validateUUID(id);
    const newsToRemove = await this.newsRepository.findOne({ where: { id: validId } });

    if (!newsToRemove) {
      throw new NotFoundException('News not found');
    }

    if (newsToRemove) {
      try {
        // Delete the image file from the server
        if (newsToRemove.featuredImage) {
          const imagePath = path.join(__dirname, "../../resource/" + newsToRemove.featuredImage);
          if (existsSync(imagePath)) {
            console.log("deleting Image in server ...: " + imagePath);
            unlinkSync(imagePath);
          }
        }

        await this.newsRepository.remove(newsToRemove);
        return newsToRemove;

      } catch (error) {
        throw error;
      }
    }

    return null;
  }
}