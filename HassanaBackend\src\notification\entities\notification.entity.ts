import { BaseEntity } from '@app/BaseEntity';
import { Colum<PERSON>, Entity, JoinColumn, ManyToOne, OneToMany, Unique } from 'typeorm';
import { User } from '@app/users/entities/user.entity';


@Entity({ name: 'notification' })
export class NotificationEntity extends BaseEntity {

  @Column()
  notification: string;

  @OneToMany(() => NotificationViewEntity, (views) => views.user)
  views: NotificationViewEntity[];
}



@Entity({ name: 'notification_view' })
export class NotificationViewEntity extends BaseEntity {

  @ManyToOne(() => NotificationEntity,(notification) => notification.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'notificationId' })
  notification: NotificationEntity;

  @ManyToOne(() => User, (user) => user.id)
  @JoinColumn({ name: 'userId' })
  user: User;

}