import { BaseEntity } from '@app/BaseEntity';
import { Category } from '@app/category/entities/category.entity';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class Event extends BaseEntity {
  @Column()
  title: string;

  @Column()
  details: string;

  // @ManyToOne(() => Category)
  // @JoinColumn({ name: 'categoryId' })
  @Column()
  category: string; 
  // category: Category; 

  @Column()
  status: Boolean;

  @Column()
  date: Date;
}
