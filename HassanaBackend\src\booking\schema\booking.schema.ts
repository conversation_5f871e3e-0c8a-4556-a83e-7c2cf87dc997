import { ObjectType, Field, Int, ID } from '@nestjs/graphql';
import { ResourceSchema } from '@app/resource/schema/resource.schema';
import { UserSchema } from '@app/users/schema/user.schema';
import { UUID } from 'crypto';

@ObjectType()
export class BookingSchema {
  @Field(() => ID)
  id: UUID;

  @Field()
  title: string;

  @Field(() => String, { nullable: true })
  details?: string;

  // @Field(() => Int)
  // resourceId: number;
  @Field()
  uid: string;
  
  @Field(() => String, { nullable: true })
  registrationDoc: string;
  
  @Field(() => String, { nullable: true })
  location: string;

  @Field(() => ID)
  userId: UUID;

  @Field()
  start: Date;

  @Field()
  end: Date;

  // @Field()
  // status: string;

  @Field()
  teaBoy: String;

  @Field()
  parking: String;

  @Field()
  itTechnician:String;

  // @Field(() => ResourceSchema)
  // resource: ResourceSchema;

  // @Field(() => UserSchema)
  // user: UserSchema;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

