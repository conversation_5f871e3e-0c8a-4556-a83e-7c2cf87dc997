// Import the ews-javascript-api module
var EWS = require('ews-javascript-api');

// Exchange service configuration
var exch = new EWS.ExchangeService(EWS.ExchangeVersion.Exchange2013);
exch.Credentials = new EWS.WebCredentials('username', 'password', 'domain');
exch.Url = new EWS.Uri('https://<your-exchange-server>/EWS/Exchange.asmx');

// Define the start and end date for the calendar view
var startDate = new Date();
var endDate = new Date();
endDate.setDate(startDate.getDate() + 7); // Fetching one week of calendar data

// Prepare the calendar view
var view = new EWS.CalendarView(startDate, endDate);

// Folder ID (use WellKnownFolderName.Calendar for default calendar)
var folderId = new EWS.FolderId(EWS.WellKnownFolderName.Calendar);

// Fetch calendar items
exch.FindAppointments(folderId, view).then(
  function (appointments) {
    appointments.Items.forEach(function (appointment) {
      console.log('Subject: ' + appointment.Subject);
      console.log('Start: ' + appointment.Start);
      console.log('End: ' + appointment.End);
      // Add more fields as needed
    });
  },
  function (err) {
    console.log(err);
  },
);
