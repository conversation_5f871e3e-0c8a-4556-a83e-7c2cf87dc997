import { BaseEntity } from '@app/BaseEntity';
import { ObjectType, Field, Int } from '@nestjs/graphql';
import { Column, Entity } from 'typeorm';

@Entity()
export class Leave extends BaseEntity {
  @Column()
  userId: number;

  @Column()
  username: string;

  @Column({ nullable: true })
  remarks: string;

  @Column()
  date: Date;

  @Column()
  numberOfDays: number;

  @Column()
  typeOfLeave: string;

  // @Column()
  // status: string;
}