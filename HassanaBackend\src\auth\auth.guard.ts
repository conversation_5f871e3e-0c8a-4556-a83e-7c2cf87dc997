import {
    CanActivate,
    ExecutionContext,
    HttpException,
    HttpStatus,
    Injectable,
} from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Observable } from 'rxjs';
import { User } from 'src/users/entities/user.entity';
import { UserService } from 'src/users/user.service';

@Injectable()
export class AuthGuard implements CanActivate {
    constructor(private readonly userService: UserService) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        try {
            const ctx = GqlExecutionContext.create(context).getContext();
            const { username, password } = ctx.req.body.variables;
            // const user : User = await this.userService.findUserByEmail(email, password);
            // if (user && user.password === password) {
            //     ctx.user = user
            return true;
            // }
            // else {
            //     throw new HttpException("UnAuthenticated", HttpStatus.UNAUTHORIZED)
            // }

        } catch (error) {
            console.log(error.message);

        }
    }
}
