import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { AnnouncementService } from './announcement.service';
import { CreateAnnouncementInput } from './dto/create-announcement.input';
import { UpdateAnnouncementInput } from './dto/update-announcement.input';
import { UUID } from 'crypto';
import { JwtGuard } from '@app/auth/jwt.guard';
import { Args } from '@nestjs/graphql';
import { ApiBody } from '@nestjs/swagger';

let multerOptions = {
    storage: diskStorage({
        destination: './resource/v1/announcement',
        filename: (req, image, callback) => {
            const uniqueSuffix = Date.now() + Math.round(Math.random() * 1e3);
            const ext = extname(image.originalname);
            const fileName = `${uniqueSuffix}${ext}`.toString();
            callback(null, fileName);
        }
    })
}

@UseGuards(JwtGuard)
@Controller('v1/our-announcement')
export class AnnouncementController {
    constructor(private readonly announcementService: AnnouncementService) { }

    @ApiBody({ type: CreateAnnouncementInput})
    @Post()
    @UseInterceptors(FileInterceptor('image', multerOptions))
    async createAnnouncement(@UploadedFile() file: Express.Multer.File, @Body() createAnnouncementInput: CreateAnnouncementInput) {
        try {
            let path = file?.path;
            let image = path?.replace(/resource\/v1[\/\\]/g, "");
            let data = await this.announcementService.create({ ...createAnnouncementInput, image });
            let Response = {
                code: 200,
                message: "Success",
                data: data
            };
            return Response;
        } catch (error) {
            console.log(error);
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
            return Response;
        }
    }

    @Get()
    async findAllAnnouncements(@Req() req: Request) {
        try {
            const { id } = req['user'];
            let data = await this.announcementService.findAll(id);
            if (data) {
                let Response = {
                    code: 200,
                    message: "Success",
                    data: data
                };
                return Response;
            }

        } catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
            return Response;
        }
    }

    @Post("/view/:announcement_id")
    async createAnnouncementView(@Param('announcement_id') announcement_id: UUID, @Req() req: Request) {
        try {
            const { id } = req['user'];
            await this.announcementService.createAnnouncementView(announcement_id, id);
            const data = await this.announcementService.findOne(announcement_id);
            let Response = {
                code: 200,
                message: "Success",
                data: data
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
            return Response;
        }
    }

    @Get(':id')
    async findOneAnnouncement(@Param('id') id: UUID) {
        try {
            let data = await this.announcementService.findOne(id);
            let Response = {
                code: 200,
                message: "Success",
                data: data
            }
            return Response;

        } catch (error) {
            let Response = {
                code: error?.code,
                mesage: error?.message,
                error: error
            }
            return Response;
        }
    }

    @ApiBody({ type: UpdateAnnouncementInput })
    @Patch(':id')
    @UseInterceptors(FileInterceptor('image', multerOptions))
    async updateAnnouncement(@Param('id') id: UUID, @Body() updateAnnouncementInput: UpdateAnnouncementInput, @UploadedFile() file: Express.Multer.File,) {
        try {
            let image : string;
            if (file) {
                let path = file?.path;
                image = path?.replace(/resource\/v1[\/\\]/g, "");
                console.log("image path", image);
            }
            // console.log({ ...updateAnnouncementInput, image });
            let data = await this.announcementService.update(id, { ...updateAnnouncementInput, image });
            data.image = `${process.env.SERVER_URL}/${data.image}`;
            let Response = {
                code: 200,
                message: "Success",
                data: data
            }
            return Response;

        } catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            }
            return Response;
        }

    }

    @Delete(':id')
    async removeAnnouncement(@Param('id') id: UUID) {
        try {
            let data = await this.announcementService.remove(id);
            let Response = {
                code: 200,
                message: "Success",
                data: data
            }
            return Response;

        } catch (error) {
            let Response = {
                code: error?.code,
                mesage: error?.message,
                error: error
            }
            return Response;
        }
    }
}
