import { Resolver, Query, Mutation, Args, Int,ID } from '@nestjs/graphql';
import { QuoteService } from './quote.service';
import { CreateQuoteInput } from './dto/create-quote.input';
import { UpdateQuoteInput } from './dto/update-quote.input';
import { QuoteSchema } from './schema/quote.schema';
import { UUID } from 'crypto';

@Resolver(() => QuoteSchema)
export class QuoteResolver {
  constructor(private readonly quoteService: QuoteService) {}

  @Mutation(() => QuoteSchema)
  async createQuote(@Args('createQuoteInput') createQuoteInput: CreateQuoteInput) {
    try {
      let data = await this.quoteService.create(createQuoteInput);
      return data;
      
    } catch (error) {
      console.log("error in resolver", error);
      return error;
    }
  }

  @Query(() => [QuoteSchema])
  findAllQuote() {
    return this.quoteService.findAll();
  }

  @Query(() => QuoteSchema)
  findOneQuote(@Args('id', { type: () => ID }) id: number) {
    return this.quoteService.findOne(id);
  }
  @Query(() => QuoteSchema)
  todaysQuote() {
    // const today = new Date();
    return this.quoteService.findByVisibility();
  }

  @Mutation(() => QuoteSchema)
  updateQuote(@Args('id', { type: () => ID }) id: UUID, @Args('updateQuoteInput') updateQuoteInput: UpdateQuoteInput) {
    return this.quoteService.update(id, updateQuoteInput);
  }

  @Mutation(() => QuoteSchema)
  removeQuote(@Args('id', { type: () => ID }) id: UUID) {
    return this.quoteService.remove(id);
  }
}
