import { Injectable } from '@nestjs/common';
import { CreateUserReviewDto } from './dto/create-user-review.dto';
import { UpdateUserReviewDto } from './dto/update-user-review.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UserReview } from './entities/user-review.entity';
import { Between, Repository } from 'typeorm';
import { UUID } from 'crypto';


@Injectable()
export class UserReviewsService {
  constructor(@InjectRepository(UserReview) private reviewRepository: Repository<UserReview> ) {}

  async create(createUserReviewDto: CreateUserReviewDto, user_id: UUID): Promise<UserReview> {
    if(createUserReviewDto.user_id === user_id) throw Error("user can't give review to his own profile");
    const now = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setUTCDate(thirtyDaysAgo.getUTCDate() - 30);
    let existingReviews = await this.findAll({
      // user_id: createUserReviewDto.user_id,
      created_by: user_id,
      createdAt: Between(thirtyDaysAgo, now)
    });
    createUserReviewDto['created_by'] = user_id;
    if(existingReviews.length >= 3) throw Error("you are not allowed to review more than a three times in a month")
    const create = this.reviewRepository.create(createUserReviewDto);
    return await this.reviewRepository.save(create);
  }

  async findAll(filter?: object){
    const data = await this.reviewRepository.find({where: filter});
    return data;
  }

  findOne(id: number) {
    return `This action returns a #${id} userReview`;
  }

  update(id: number, updateUserReviewDto: UpdateUserReviewDto) {
    return `This action updates a #${id} userReview`;
  }

  remove(id: number) {
    return `This action removes a #${id} userReview`;
  }
}
