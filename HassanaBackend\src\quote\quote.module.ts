import { Module } from '@nestjs/common';
import { QuoteService } from './quote.service';
import { QuoteResolver } from './quote.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuoteEntity } from './entities/quote.entity';

@Module({
  imports: [TypeOrmModule.forFeature([QuoteEntity])],
  controllers:[],
  providers: [QuoteResolver, QuoteService],
  exports:[QuoteService]

})
export class QuoteModule {}
