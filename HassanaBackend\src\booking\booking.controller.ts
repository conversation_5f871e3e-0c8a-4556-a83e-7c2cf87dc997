import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    UploadedFile,
    UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { BookingService } from './booking.service';
import { CreateBookingInput } from './dto/create-booking.input';

let multerOptions = {
    storage: diskStorage({
        destination: './v1/resource/booking',
        filename: (req, featuredImage, callback) => {
            const uniqueSuffix = Date.now() + Math.round(Math.random() * 1e3);
            const ext = extname(featuredImage.originalname);
            const fileName = `${uniqueSuffix}${ext}`.toString();
            callback(null, fileName);
        },
    }),
};

@Controller('v1/our-booking')
export class BookingController {
    constructor(private readonly bookingService: BookingService) { }

    @Post()
    @UseInterceptors(FileInterceptor('registrationDoc', multerOptions))
    async createBooking(
        @UploadedFile() file: Express.Multer.File,
        @Body() body: any,
    ) {
        try {
            let createBookingInput: CreateBookingInput;

            // Check if data is sent as form data with CreateBookingInput field
            if (body.CreateBookingInput) {
                createBookingInput = JSON.parse(body.CreateBookingInput);
            } else {
                // Direct JSON body
                createBookingInput = body;
            }
		console.log("+++++++++++++++",createBookingInput)
            if (file) {
                let path = file?.path;
                var registrationDoc = path?.replace(/resource\/v1[\/\\]/g, "");;
            }

            // Await the asynchronous operation
            let data = await this.bookingService.create({
                ...createBookingInput,
                registrationDoc,
            });

            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        } catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError,
            };
            return Response;
        }
    }
}
