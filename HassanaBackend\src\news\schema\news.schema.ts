import { Field, GraphQLISODateTime, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class NewsSchema {
    @Field()
    id: string;

    @Field()
    title: string;

    @Field()
    url: string;

    @Field()
    category: string;

    @Field()
    featuredImage: string;

    @Field()
    summary: string;

    @Field()
    author: string;

    @Field()
    source: string;

    @Field()
    status: string;

    @Field((type) => GraphQLISODateTime)
    visibility: Date;

    @Field((type) => GraphQLISODateTime)
    publication: Date;

    @Field((type) => GraphQLISODateTime)
    created_on: Date;

    @Field()
    created_by: string;

    @Field((type) => GraphQLISODateTime)
    updated_on: Date;

    @Field()
    updated_by: string;

    // BaseEntity fields
    @Field((type) => GraphQLISODateTime)
    createdAt: Date;

    @Field((type) => GraphQLISODateTime)
    updatedAt: Date;
}