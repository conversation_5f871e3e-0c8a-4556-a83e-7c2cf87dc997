import { Body, Controller, Get, Param, Patch, Post, Query, UploadedFile, UseInterceptors } from '@nestjs/common';
import { Args } from '@nestjs/graphql';
import { UserService } from './user.service';
// import { User } from './entities/user.entity';
// import { UpdateUserInput } from './dto/update-user.input';
import { Logger } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import * as jwt from 'jsonwebtoken';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { LoginUser } from './dto/login-user';
import { UpdateUserInput } from './dto/update-user.input';
import { User } from './entities/user.entity';
import { UUID } from 'crypto';
import { ApiBody } from '@nestjs/swagger';


let multerOptions = {
    storage: diskStorage({
        destination: './resource/v1/users',
        filename: (req, profile, callback) => {
            const uniqueSuffix = Date.now() + Math.round(Math.random() * 1e3);
            const ext = extname(profile.originalname);
            const fileName = `${uniqueSuffix}${ext}`.toString();
            callback(null, fileName);
        }
    })
}

@Controller('v1/app-users')
export class UsersController {
    constructor(private readonly userService: UserService) { }

    // @Query('page') page: number = 1,
    // @Query('pageSize') pageSize: number = 10,
    
    @Get()
    async getAllUsers(@Query('page') page: number = 1, @Query('pageSize') pageSize: number = 10) {
        try {
            const { users, meta } = await this.userService.findAllUsers(page ?? 1, pageSize ?? 100);
            return {
                code: 200,
                message: "Success",
                data: users,
                meta
            };
        } catch (error) {
            console.log(error);
            return {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
        }
    }


    // @Post()
    // createUser() {
    //     return this.userService.createUser();
    // }

    //   @Query(() => [User], { name: 'users' })
    //   findAll() {
    //     return this.usersService.findAll();
    //   }

    //   @Query(() => User, { name: 'user' })
    //   findOne(@Args('id') id: string) {
    //     return this.usersService.findOne(id);
    //   }

    @ApiBody({ type: UpdateUserInput })
    @Patch('/:id')
    @UseInterceptors(FileInterceptor('profile', multerOptions))
    async updateUser(@Param('id') id: UUID, @Body() updateUserInput: UpdateUserInput, @UploadedFile() file: Express.Multer.File,) {
        try {
            console.log("file: " + file);
            if (file) {
                let path = file?.path;
                var profile = path?.replace(/resource\/v1[\/\\]/g, "");;
            }
            // console.log({ ...updateAnnouncementInput, image });
            let data = await this.userService.update(id, { ...updateUserInput, profile });
            // data.profile = `${process.env.SERVER_URL}/${data.profile}`;
            let Response = {
                code: 200,
                message: "Success",
                data: data
            }
            return Response;

        } catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            }
            return Response;
        }
        
        // return this.userService.update(updateUserInput.id, updateUserInput);
    }

    //   @Mutation(() => User)
    //   removeUser(@Args('id', { type: () => Int }) id: number) {
    //     return this.usersService.remove(id);
    //   }
}