import { Module } from '@nestjs/common';
import { UserFeedbackService } from './user-feedback.service';
import { UserFeedbackController } from './user-feedback.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserFeedback } from './entities/user-feedback.entity';

@Module({
  imports: [TypeOrmModule.forFeature([UserFeedback])],
  controllers: [UserFeedbackController],
  providers: [UserFeedbackService],
})
export class UserFeedbackModule {}
