import { InputType, Field, ID } from '@nestjs/graphql';
import { IsString, IsBoolean, IsDate, IsOptional, IsUUID } from 'class-validator';

@InputType()
export class UpdateEventInput {
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  title?: string;

  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  details?: string;

  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  category?: string;

  @Field({ nullable: true })
  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  date?: Date;
}