import { Resolver, Query, Mutation, Args, Int,ID } from '@nestjs/graphql';
import { EventService } from './event.service';
import { CreateEventInput } from './dto/create-event.input';
import { UpdateEventInput } from './dto/update-event.input';
import { Event } from './schema/event.schema';
import { UUID } from 'crypto';

@Resolver(() => Event)
export class EventResolver {
  constructor(private readonly eventService: EventService) { }

  @Mutation(() => Event)
  createEvent(@Args('createEventInput') createEventInput: CreateEventInput) {
    return this.eventService.create(createEventInput);
  }

  @Query(() => [Event], { name: 'events' })
  findAll() {
    return this.eventService.findAll();
  }

  @Query(() => Event, { name: 'event' })
  findOne(@Args('id', { type: () => ID }) id: UUID) {
    return this.eventService.findOne(id);
  }
  // @Query(() => [Event], { name: 'todaysEvents' })
  // TodaysEvents(@Args('date', { type: () => Date }) date: Date, category: string) {
  //   return this.eventService.findTodaysEvent(date, category);
  // }

  @Query(() => [Event], { name: 'todaysEvents' })
  TodaysEvents(@Args('date', { type: () => Date }) date: Date, @Args('category') category: string) {
    return this.eventService.findTodaysEvent(date, category);
  }

  @Mutation(() => Event)
  updateEvent(@Args('id', { type: () => ID }) id: UUID, @Args('updateEventInput') updateEventInput: UpdateEventInput) {
    return this.eventService.update(id, updateEventInput);
  }

  @Mutation(() => Event)
  removeEvent(@Args('id', { type: () => ID }) id: UUID) {
    return this.eventService.remove(id);
  }
}

