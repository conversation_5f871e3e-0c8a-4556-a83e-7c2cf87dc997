import { Injectable } from '@nestjs/common';
import { CreateCategoryInput } from './dto/create-category.input';
import { UpdateCategoryInput } from './dto/update-category.input';
import { InjectRepository } from '@nestjs/typeorm';
import { Category } from './entities/category.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';

@Injectable()
export class CategoryService {

  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) { }

  async create(createCategoryInput: CreateCategoryInput): Promise<Category> {
    const newCategory = this.categoryRepository.create({
      ...createCategoryInput
    });

    const savedCategory = await this.categoryRepository.save(newCategory);

    return { ...savedCategory, id: savedCategory.id };
  }

  findAll(): Promise<Category[]> {
    return this.categoryRepository.find();
  }

  findOne(id: UUID): Promise<Category> {
    return this.categoryRepository.findOne({ where: { id } });
  }

  async update(id: UUID, updateCategoryInput: UpdateCategoryInput) {
    const existingCategory = await this.categoryRepository.findOne({ where: { id } });

    if (existingCategory) {
      this.categoryRepository.merge(existingCategory, updateCategoryInput);
      return this.categoryRepository.save(existingCategory);
    }

    return null;
  }

  async remove(id: UUID): Promise<Category | null>  {
    const categoryToRemove = await this.categoryRepository.findOne({ where: { id } });

    if (categoryToRemove) {
      await this.categoryRepository.remove(categoryToRemove);
      return categoryToRemove;
    }

    return null;
  }
}
