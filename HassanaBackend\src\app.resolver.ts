import { UseGuards } from '@nestjs/common';
import { Args, Context, Query, Resolver } from '@nestjs/graphql';
import * as jwt from 'jsonwebtoken';
import { AuthGuard } from './auth/auth.guard';
import { JwtGuard } from './auth/jwt.guard';
import { User } from './users/entities/user.entity';
import { RoleGuard, Roles } from './auth/role.guard';

@Resolver((of) => String)
export class AppResolver {
  @Query((returns) => String)
  index(): string {
    return 'Nest JS Graphql server .....';
  }

  @Query((returns) => String)
  @UseGuards(JwtGuard, new RoleGuard(Roles.ADMIN))
  securedDataForAdmin(@Context('user') user: any): string {
    return 'This is secured data for admin ' + JSON.stringify(user);
  }

  @Query((returns) => String)
  @UseGuards(JwtGuard, new RoleGuard(Roles.USER))
  securedDataForUser(@Context('user') user: any): string {
    return 'This is secured data for user ' + JSON.stringify(user);
  }

  @Query((returns) => String)
  @UseGuards(AuthGuard)
  login(
    @Args({ name: 'email', type: () => String }) email: string,
    @Args({ name: 'password', type: () => String }) password: string,
    @Context('user') user: User,
  ): string {
    let payload = {
      id: user.id,
      name: user.name,
      username: user.user_principal_name,
      role: user.role
    }

    return jwt.sign(payload, 'key', { expiresIn: '60s' });
  }
}
