import { Controller, Get, Query, Res } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { Response } from 'express';
import {redis} from '../../redis';

@Controller('/v1/test')
export class TestController {
  @Get('testing-route') 
  async getTesting(
    @Query('lat') lat: number,
    @Query('lon') lon: number,
    @Res() res: Response,
  ) {
    // Creating a unique key based on latitude and longitude
    const redisKey = `weather:${lat}:${lon}`;

    // Checking for nearby cached locations within a 10 km radius
    const nearbyLocations = (await redis.georadius(
      'weather_locations',
      lon,
      lat,
      5,
      'km',
    )) as string[];

    for (let location of nearbyLocations) {
      if (typeof location === 'string') {
        let cacheData = await redis.get(location);
        if (cacheData) {
          console.log('Response from Redis');

          return res.json({
            code: 200,
            message: 'Successfully retrieved from cache',
            data: JSON.parse(cacheData),
            source: 'cache',
          });
        }
      }
    }

    // Weather API endpoint using latitude and longitude
    const weatherAPI = `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${process.env.WEATHER_API_KEY}`;

    try {
      const response = await axios.get(weatherAPI);

      // Storing response in Redis with 2-hour expiry and geospatial indexing
      await redis.geoadd('weather_locations', lon, lat, redisKey);
      await redis.set(redisKey, JSON.stringify(response.data), 'EX', 7200);
      console.log('Response from API');

      return res.json({
        code: 200,
        message: 'Successfully retrieved from API',
        data: response.data,
        source: 'API',
      });
    } catch (error) {
      return res.json({
        code: 400,
        message: 'Something went wrong',
        error: error.message,
      });
    }
  }
}

// constructor(private readonly testService: TestService) {}

// @Get('testing-route')
// async getTesting(@Query() query: Record<any, any>, @Res() res: Response) {
//   const city = query.city;

//   let cacheData = await redis.get(`weather: ${city}`);

//   if (cacheData) {
//     let cacheDataJSON = JSON.parse(cacheData)
//     const result =  { ...cacheDataJSON  , source: "cache"}
//     return (
//       res.json({
//         code: 200,
//         message: "successfully response completed",
//         data: result
//       })
//     )
//   }

//   const weatherAPI = (city: string) => `https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${process.env.WEATHER_API_KEY}`

//   const initialTime = new Date().getTime();

//   axios.get(weatherAPI(city))
//     .then((response: AxiosResponse) => {

//       const finalTime = new Date().getTime();
//       const resposeTime = `${finalTime - initialTime} ms`
//       const result = { ...response.data, source: "API", responseTime: resposeTime }
//       redis.set(`weather: ${city}`, JSON.stringify(response.data), 'EX', 3600)

//       res.json({
//         code: 200,
//         message: "successfully response completed",
//         data: result
//       });

//     })
//     .catch((error: any) => {

//       res.json({
//         code: 400,
//         message: "Something went wrong",
//         error: error.message,
//       });

//     })

//   console.log(query);
//   // return {
//   //   data: query
//   // };
// }
//
// }
