import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BookingService } from './booking.service';
import { BookingResolver } from './booking.resolver';
import { Booking } from './entities/booking.entity'; // make sure to adjust this import to your file structure
import { Resource } from '@app/resource/entities/resource.entity';
import { BookingController } from './booking.controller';
import { User } from '@app/users/entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Booking, User])],
  controllers: [BookingController],
  providers: [BookingResolver, BookingService],
  exports: [BookingService],
})
export class BookingModule { }
