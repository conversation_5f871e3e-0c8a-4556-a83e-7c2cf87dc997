import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateUserFeedbackDto } from './dto/create-user-feedback.dto';
import { UpdateUserFeedbackDto } from './dto/update-user-feedback.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UserFeedback } from './entities/user-feedback.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
import { sendMail } from '@app/Emailer';

@Injectable()
export class UserFeedbackService {
  constructor(@InjectRepository(UserFeedback) private feedbackRepository: Repository<UserFeedback>) {}

  async create(createUserFeedbackDto: CreateUserFeedbackDto, userId: UUID): Promise<UserFeedback> {
    sendMail({
      from: "<EMAIL>",
      to: "<EMAIL>",
      subject: `${createUserFeedbackDto.type} - ${createUserFeedbackDto.subject}`,
      text: createUserFeedbackDto.description,
      html: null
    })
    const create = this.feedbackRepository.create({
      ...createUserFeedbackDto,
      user_id: userId
    });
    return await this.feedbackRepository.save(create);
  };

  async findAll(): Promise<UserFeedback[]> {
    return await this.feedbackRepository.find();
  };

  async findOne(id: UUID) {
    const resource = await this.feedbackRepository.findOne({ where: { id } });
    if (!resource) {
      throw new NotFoundException(`Resource with ID ${id} not found`);
    }
    return resource;
  };

  // update(id: number, updateUserFeedbackDto: UpdateUserFeedbackDto) {
  //   return `This action updates a #${id} userFeedback`;
  // }

  async remove(id: UUID): Promise<UserFeedback> {
    const toDelete = await this.findOne(id);
    await this.feedbackRepository.remove(toDelete);
    return toDelete;
  };
}
