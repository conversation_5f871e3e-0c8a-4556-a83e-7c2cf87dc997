const axios = require('axios');
const https = require('https');

let data = '<?xml version="1.0" encoding="utf-8"?>\n      <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n                     xmlns:m="http://schemas.microsoft.com/exchange/services/2006/messages"\n                     xmlns:t="http://schemas.microsoft.com/exchange/services/2006/types"\n                     xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">\n          <soap:Header>\n              <t:RequestServerVersion Version="Exchange2010" />\n          </soap:Header>\n          <soap:Body>\n              <m:FindItem Traversal="Shallow">\n                  <m:ItemShape>\n                      <t:BaseShape>AllProperties</t:BaseShape>\n                      <t:AdditionalProperties>\n                          <t:FieldURI FieldURI="item:Subject" />\n                          <t:FieldURI FieldURI="item:Body" />\n                          <t:FieldURI FieldURI="calendar:LegacyFreeBusyStatus" />\n                          <t:FieldURI FieldURI="calendar:Start" />\n                          <t:FieldURI FieldURI="calendar:End" />\n                          <t:FieldURI FieldURI="calendar:UID" />\n                          <t:FieldURI FieldURI="calendar:Location" />\n                      </t:AdditionalProperties>\n                  </m:ItemShape>\n                  <m:CalendarView MaxEntriesReturned="50"\n                                  StartDate="2024-01-01T00:00:00Z"\n                                  EndDate="2024-12-31T23:59:59Z" />\n                  <m:ParentFolderIds>\n                      <t:DistinguishedFolderId Id="calendar" />\n                  </m:ParentFolderIds>\n              </m:FindItem>\n          </soap:Body>\n      </soap:Envelope>';

let config = {
  method: 'post',
  maxBodyLength: Infinity,
    username: "<EMAIL>",
  password: "Xamilox@81",
  url: 'https://mail.hassana.com.sa/EWS/Exchange.asmx',
  headers: {
    'Content-Type': 'text/xml; charset=utf-8',
    'SOAPAction': 'http://schemas.microsoft.com/exchange/services/2006/messages/FindItem'
  },
  data: data,
  httpsAgent: new https.Agent({
    rejectUnauthorized: false
  })
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

