import { Injectable } from '@nestjs/common';
import { CreateLibraryInput } from './dto/create-library.input';
import { UpdateLibraryInput } from './dto/update-library.input';
import { InjectRepository } from '@nestjs/typeorm';
import { Library } from './entities/library.entity';
import { Repository } from 'typeorm';
import * as path from 'path';
import { existsSync, unlinkSync } from 'fs';
import { UUID } from 'crypto';

@Injectable()
export class LibraryService {

  constructor(@InjectRepository(Library) private readonly libraryRepository: Repository<Library>) {}

  async create(createLibraryInput: CreateLibraryInput): Promise<Library> {
    const newFile = this.libraryRepository.upsert({
      ...createLibraryInput
    }, {conflictPaths: ["file_name"]});
    // const savedFile = await this.libraryRepository.save(newFile);
    return (await newFile).raw
  }

  async findAll(): Promise<Library[]> {
    const result = await this.libraryRepository.find();
    result.forEach((obj, index, array) => {
      array[index] = {
        ...obj,
        file_path: `${process.env.SERVER_url}/${obj.file_path}`
      }
    })
    return result;
  }

  async remove(id: UUID) : Promise<Library> {
    const find = await this.libraryRepository.findOne({where: {id}});
    const filePath = path.join(__dirname, "../../../library/v1/" + find.file_path);
    console.log(filePath);
    if(!find) throw Error("library not found");
    if (existsSync(filePath)) {
      console.log("deleting file in server ...: " + filePath);
      unlinkSync(filePath);
    }
    await this.libraryRepository.remove(find);
    return ;
  }
}
