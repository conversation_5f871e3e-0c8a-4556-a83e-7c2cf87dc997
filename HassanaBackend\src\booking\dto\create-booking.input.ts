import { Field, ID, InputType, Int } from '@nestjs/graphql';
import { UUID } from 'crypto';
@InputType()
export class CreateBookingInput {
  @Field()
  title: string;

  @Field(() => String, { nullable: true })
  details?: string;

  @Field()
  uid: string;

  @Field(() => String, { nullable: true })
  registrationDoc: string;

  @Field(() => String, { nullable: true })
  location: string;

  @Field(() => ID)
  userId: UUID;

  @Field()
  teaBoy: string;

  @Field()
  parking: string;

  @Field()
  itTechnician: string;

  @Field()
  start: Date;

  @Field()
  end: Date;
}
