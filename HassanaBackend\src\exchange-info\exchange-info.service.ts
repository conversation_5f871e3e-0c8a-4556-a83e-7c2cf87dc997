import { Injectable } from '@nestjs/common';
import * as httpntlm from 'httpntlm';
import { NtlmClient } from 'axios-ntlm';
import { Redis } from 'ioredis';
import * as xml2js from 'xml2js';
import { UpdateExchangeInfoDto } from './dto/update-exchange-info.dto';
import { redis } from '../../redis';
import { UUID } from 'crypto';


@Injectable()
export class ExchangeInfoService {
  // create(createExchangeInfoDto: CreateExchangeInfoDto) {
  //   return 'This action adds a new exchangeInfo';
  // }

  findAll() {
    return `This action returns all exchangeInfo`;
  }

  async findOne(id: string) {
    let redisValue = await redis.hgetall(`user: ${id}`);
    console.log(redisValue);
    let DataForUser = await this.fetchDataForUser(
      redisValue.username,
      redisValue.password,
    );
    return DataForUser;
    
  }

  update(id: number, updateExchangeInfoDto: UpdateExchangeInfoDto) {
    return `This action updates a #${id} exchangeInfo`;
  }

  remove(id: number) {
    return `This action removes a #${id} exchangeInfo`;
  }

  private url: string = 'https://mail.hassana.com.sa/EWS/Exchange.asmx';

  async fetchDataForUser(username: string, password: string): Promise<any> {
    const axiosNTLM = NtlmClient({
      username: username,
      password: password,
      domain: '',
      workstation: '',
    });

    const options = {
      method: 'post',
      url: this.url,
      headers: {
        Accept: 'application/json, text/plain, */*',
        'Content-Type': 'text/xml; charset=utf-8',
        SOAPAction: 'http://schemas.microsoft.com/exchange/services/2006/messages/FindItem',
      },
      data: this.createRequestBody(),
    };

    try {
      const res = await axiosNTLM(options);
      const parsedData = await this.parseXMLResponse(res.data);
      if (parsedData) {
        const errorCheck =
          parsedData['s:Envelope']['s:Body']['m:FindItemResponse'][
            'm:ResponseMessages'
          ]['m:FindItemResponseMessage']['m:ResponseCode'];
        console.log('ErrorCheck' + errorCheck);

        let data = [];
        if (errorCheck == 'NoError') {
          let jsonData =
            parsedData['s:Envelope']['s:Body']['m:FindItemResponse'][
              'm:ResponseMessages'
            ]['m:FindItemResponseMessage']['m:RootFolder']['t:Items'][
              't:CalendarItem'
            ];

          // If jsonData is not an array, convert it to an array
          if (!Array.isArray(jsonData)) {
            jsonData = [jsonData];
          }

          jsonData.map((item: any) => {
            const returnObject = {
              title: item['t:Subject'],
              status: item['t:LegacyFreeBusyStatus'],
              description: item['t:Body'],
              t_start: item['t:Start'],
              t_end: item['t:End'],
              uid: item['t:UID'],
              location: item['t:Location'],
            };
            data.push(returnObject);
          });
          // console.log(data);
          
          return data;
        } else {
          return data;
        }
      } else {
        throw Error(`No exchange data found against ${username} username`);
      }
    } catch (error) {
      console.error(error.message);
      throw error;
    }
  }
  private createRequestBody(): string {
    return `<?xml version="1.0" encoding="utf-8"?>
      <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xmlns:m="http://schemas.microsoft.com/exchange/services/2006/messages"
                     xmlns:t="http://schemas.microsoft.com/exchange/services/2006/types"
                     xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
          <soap:Header>
              <t:RequestServerVersion Version="Exchange2010" />
          </soap:Header>
          <soap:Body>
              <m:FindItem Traversal="Shallow">
                  <m:ItemShape>
                      <t:BaseShape>AllProperties</t:BaseShape>
                      <t:AdditionalProperties>
                          <t:FieldURI FieldURI="item:Subject" />
                          <t:FieldURI FieldURI="item:Body" />
                          <t:FieldURI FieldURI="calendar:LegacyFreeBusyStatus" />
                          <t:FieldURI FieldURI="calendar:Start" />
                          <t:FieldURI FieldURI="calendar:End" />
                          <t:FieldURI FieldURI="calendar:UID" />
                          <t:FieldURI FieldURI="calendar:Location" />
                      </t:AdditionalProperties>
                  </m:ItemShape>
                  <m:CalendarView 
                                  StartDate="${new Date().getFullYear()}-01-01T00:00:00Z"
                                  EndDate="${new Date().getFullYear()}-12-31T23:59:59Z" />
                  <m:ParentFolderIds>
                      <t:DistinguishedFolderId Id="calendar" />
                  </m:ParentFolderIds>
              </m:FindItem>
          </soap:Body>
      </soap:Envelope>`;
  };

  private parseXMLResponse(xmlBody: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const parser = new xml2js.Parser({
        explicitArray: false,
        ignoreAttrs: true,
      });
      parser.parseString(xmlBody, (err, result) => {
        if (err) {
          console.error('Error parsing XML:', err);
          return reject(err);
        }
        resolve(result);
      });
    });
  };
}
