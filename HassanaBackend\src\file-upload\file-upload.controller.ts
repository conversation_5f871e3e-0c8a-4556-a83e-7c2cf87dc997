import { 
  Controller, 
  Post, 
  UploadedFile, 
  UseInterceptors, 
  Body, 
  Get, 
  Query, 
  BadRequestException,
  Delete,
  NotFoundException,
  Res,
  StreamableFile,
  Patch
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import { createReadStream, statSync } from 'fs';
import * as mime from 'mime-types';

@Controller('v1/file-system')
export class FileSystemController {
  private readonly basePath = path.join(process.cwd(), 'library', 'v1', 'lib');

  // Helper to get absolute path with security checks
  private getAbsolutePath(userPath: string = ''): string {
    const normalizedPath = userPath ? userPath.replace(/\.\.\//g, '') : '';
    const absolutePath = path.resolve(this.basePath, normalizedPath);
    
    if (!absolutePath.startsWith(this.basePath)) {
      throw new BadRequestException('Invalid path');
    }
    
    return absolutePath;
  }

  // Create folder endpoint
  @Post('folder')
  async createFolder(
    @Body('path') folderPath: string,
    @Body('folderName') folderName: string
  ) {
    if (!folderName) throw new BadRequestException('Folder name is required');
    
    const fullPath = this.getAbsolutePath(folderPath);
    const newFolderPath = path.join(fullPath, folderName);

    if (fs.existsSync(newFolderPath)) {
      throw new BadRequestException('Folder already exists');
    }

    fs.mkdirSync(newFolderPath, { recursive: true });
    return { 
      message: 'Folder created successfully',
      path: newFolderPath.replace(this.basePath, '') 
    };
  }

  // Upload file endpoint (supports nested folders)
  @Post('upload')
  @UseInterceptors(FileInterceptor('file', {
    limits: { fileSize: 50 * 1024 * 1024 }, // 50MB to match frontend
  }))
  uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Query('path') folderPath: string = ''
  ) {
    if (!file) throw new BadRequestException('No file uploaded');
    
    const targetFolder = this.getAbsolutePath(folderPath);
    
    if (!fs.existsSync(targetFolder)) {
      fs.mkdirSync(targetFolder, { recursive: true });
    }

    const filePath = path.join(targetFolder, file.originalname);
    fs.writeFileSync(filePath, file.buffer);

    return {
      message: 'File uploaded successfully',
      path: filePath.replace(this.basePath, ''),
      fileName: file.originalname
    };
  }

  // List contents of a folder
  @Get('list')
  listDirectory(@Query('path') folderPath: string = '') {
    const targetPath = this.getAbsolutePath(folderPath);
    
    if (!fs.existsSync(targetPath)) {
      throw new BadRequestException('Path does not exist');
    }

    const items = fs.readdirSync(targetPath);
    const result = [];

    items.forEach(item => {
      const itemPath = path.join(targetPath, item);
      const stat = fs.statSync(itemPath);
      
      result.push({
        name: item,
        type: stat.isDirectory() ? 'folder' : 'file',
        path: path.join(folderPath, item).replace(/\\/g, '/'),
        size: stat.size,
        createdAt: stat.birthtime,
        updatedAt: stat.mtime
      });
    });

    return result;
  }

  // File download endpoint
  @Get('file')
async getFile(
  @Query('path') filePath: string,
  @Query('download') download: string,
  @Res({ passthrough: true }) res: Response
) {
  if (!filePath) {
    throw new BadRequestException('File path is required');
  }

  const absolutePath = this.getAbsolutePath(filePath);

  if (!fs.existsSync(absolutePath)) {
    throw new NotFoundException('File not found');
  }

  const stat = fs.statSync(absolutePath);
  if (stat.isDirectory()) {
    throw new BadRequestException('Cannot download a directory');
  }

  // Determine the MIME type with explicit fallbacks
  let contentType = mime.lookup(absolutePath) || 'application/octet-stream';
  const ext = path.extname(absolutePath).toLowerCase();
  if (ext === '.png') contentType = 'image/png';
  if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';
  if (ext === '.txt') contentType = 'text/plain';
  console.log(`File: ${filePath}, Extension: ${ext}, Content-Type: ${contentType}`);

  // Set headers: use 'attachment' for download, 'inline' for preview
  const disposition = download === 'true' ? 'attachment' : 'inline';
  res.set({
    'Content-Type': contentType,
    'Content-Disposition': `${disposition}; filename="${encodeURIComponent(path.basename(absolutePath))}"`,
    'Content-Length': stat.size.toString(),
  });

  return new StreamableFile(createReadStream(absolutePath));
}
  // Delete item (file or folder)
  @Delete('delete')
  deleteItem(
    @Body('path') itemPath: string
  ) {
    if (!itemPath) throw new BadRequestException('Path is required');
    
    const fullPath = this.getAbsolutePath(itemPath);

    if (!fs.existsSync(fullPath)) {
      throw new BadRequestException('Path does not exist');
    }

    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      fs.rmSync(fullPath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(fullPath);
    }

    return { message: 'Item deleted successfully' };
  }

  @Patch('rename-file')
  renameFile(
    @Body('path') filePath: string,
    @Body('newName') newName: string
  ) {
    if (!filePath || !newName) {
      throw new BadRequestException('File path and new name are required');
    }

    const absolutePath = this.getAbsolutePath(filePath);
    
    if (!fs.existsSync(absolutePath)) {
      throw new NotFoundException('File not found');
    }

    const stat = fs.statSync(absolutePath);
    if (stat.isDirectory()) {
      throw new BadRequestException('Path points to a directory, use rename-folder endpoint instead');
    }

    if (!newName.match(/^[a-zA-Z0-9._-]+$/)) {
      throw new BadRequestException('Invalid file name. Use alphanumeric characters, dots, underscores, or hyphens only');
    }

    const directory = path.dirname(absolutePath);
    const newFilePath = path.join(directory, newName);

    if (fs.existsSync(newFilePath)) {
      throw new BadRequestException('A file with the new name already exists');
    }

    fs.renameSync(absolutePath, newFilePath);

    return {
      message: 'File renamed successfully',
      path: newFilePath.replace(this.basePath, ''),
      fileName: newName
    };
  }

  @Patch('rename-folder')
  renameFolder(
    @Body('path') folderPath: string,
    @Body('newName') newName: string
  ) {
    if (!folderPath || !newName) {
      throw new BadRequestException('Folder path and new name are required');
    }

    const absolutePath = this.getAbsolutePath(folderPath);
    
    if (!fs.existsSync(absolutePath)) {
      throw new NotFoundException('Folder not found');
    }

    const stat = fs.statSync(absolutePath);
    if (!stat.isDirectory()) {
      throw new BadRequestException('Path points to a file, use rename-file endpoint instead');
    }

    if (!newName.match(/^[a-zA-Z0-9._-]+$/)) {
      throw new BadRequestException('Invalid folder name. Use alphanumeric characters, dots, underscores, or hyphens only');
    }

    const directory = path.dirname(absolutePath);
    const newFolderPath = path.join(directory, newName);

    if (fs.existsSync(newFolderPath)) {
      throw new BadRequestException('A folder with the new name already exists');
    }

    fs.renameSync(absolutePath, newFolderPath);

    return {
      message: 'Folder renamed successfully',
      path: newFolderPath.replace(this.basePath, ''),
      folderName: newName
    };
  }
}
