import { InputType, Int, Field } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { UUID } from 'crypto';
import { IsString, IsOptional } from 'class-validator';

@InputType()
export class CreateUser {

  @ApiProperty()
  @Field({ nullable: true })
  profile: string;

  @ApiProperty()
  @Field({ nullable: true })
  email: string;

  @ApiProperty()
  @Field({ nullable: true })
  name: string;

  @ApiProperty()
  @Field({ nullable: true })
  name_arabic: string;

  @ApiProperty()
  @Field({ nullable: true })
  designation: string;

  @ApiProperty()
  @Field({ nullable: true })
  designation_arabic: string;

  @ApiProperty()
  @Field({ nullable: true })
  department: string;

  @ApiProperty()
  @Field({ nullable: true })
  department_arabic: string;

  @ApiProperty()
  @Field({ nullable: true })
  bio_link: string;

  @ApiProperty()
  @Field({ nullable: true })
  new_joiner: string;

  @ApiProperty()
  @Field({ nullable: false })
  status: string;

  @ApiProperty()
  @Field()
  dn: string;

  @ApiProperty()
  @Field()
  gender: string;

  @ApiProperty()
  @Field()
  account_expires: string;

  @ApiProperty()
  @Field()
  is_cultural_ambassador: string;

  @ApiProperty()
  @Field()
  user_principal_name: string;

  @ApiProperty()
  @Field()
  role: string;

  @IsOptional()
  @IsString()
  activity: string;

  @IsOptional()
  @IsString()
  extension: string;

}
