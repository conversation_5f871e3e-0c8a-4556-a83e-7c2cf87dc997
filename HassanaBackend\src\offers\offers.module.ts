import { Module } from '@nestjs/common';
import { OffersService } from './offers.service';
import { OffersResolver } from './offers.resolver';
import { Offers, OffersViewEntity } from './entities/offers.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports:[TypeOrmModule.forFeature([Offers, OffersViewEntity])],
  controllers:[],
  providers: [OffersResolver, OffersService],
  exports:[OffersService]
})
export class OfferModule {}
