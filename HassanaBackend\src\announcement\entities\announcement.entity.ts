import { BaseEntity } from '@app/BaseEntity';
import { User } from '@app/users/entities/user.entity';
import { UUID } from 'crypto';
import { Column, Entity, JoinColumn, ManyToOne, Unique } from 'typeorm';

@Entity()
export class Announcement extends BaseEntity {
  @Column()
  title: string;

  @Column()
  details: string;

  @Column()
  category: string;

  @Column()
  status: Boolean;

  @Column()
  visibility: Date;

  @Column({nullable: true})
  image: string;
}


@Entity({ name: 'announcement_view' })
  @Unique(["user_id", "announcement_id"])
export class AnnouncementViewEntity extends BaseEntity {

  @ManyToOne(() => Announcement, (announcement) => announcement.id, { onDelete: 'CASCADE', nullable: false })
  @JoinColumn({ name: 'announcement_id' })
  announcement_id: UUID;

  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user_id: UUID;

}