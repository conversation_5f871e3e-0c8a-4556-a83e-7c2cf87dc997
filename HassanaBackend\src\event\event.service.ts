import { Injectable } from '@nestjs/common';
import { CreateEventInput } from './dto/create-event.input';
import { UpdateEventInput } from './dto/update-event.input';
import { Event } from './entities/event.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { UUID } from 'crypto';

@Injectable()
export class EventService {

  constructor(
    @InjectRepository(Event)
    private readonly eventRepository: Repository<Event>,
  ) { }

  async create(createEventInput: CreateEventInput): Promise<Event> {
    const newEvent = this.eventRepository.create({
      ...createEventInput
    });

    const savedEvent = await this.eventRepository.save(newEvent);

    return { ...savedEvent, id: savedEvent.id };
  }

  findAll(): Promise<Event[]> {
    return this.eventRepository.find();
  }

  findOne(id: UUID): Promise<Event> {
    return this.eventRepository.findOne({ where: { id } });
  }

  findTodaysEvent(date: Date, category: string): Promise<Event[]> {
    const startDate = new Date(date);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date);
    endDate.setHours(23, 59, 59, 999);

    return this.eventRepository.find({ where: { date: Between(startDate, endDate), category: category, status: true } });
  }

  async update(id: UUID, updateEventInput: UpdateEventInput) {
    const existingEvent = await this.eventRepository.findOne({ where: { id } });

    if (existingEvent) {
      this.eventRepository.merge(existingEvent, updateEventInput);
      return this.eventRepository.save(existingEvent);
    }

    return null;
  }

  async remove(id: UUID): Promise<Event | null> {
    const eventToRemove = await this.eventRepository.findOne({ where: { id } });

    if (eventToRemove) {
      await this.eventRepository.remove(eventToRemove);
      return eventToRemove;
    }

    return null;
  }
}
