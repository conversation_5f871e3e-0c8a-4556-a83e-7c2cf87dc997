import { BaseEntity } from '@app/BaseEntity';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';


@Entity({name: 'quote'})
@Unique(['visibilityStart'])
export class QuoteEntity extends BaseEntity{

    @Column()
    quote: string;

    @Column()
    author: string;

    @Column()
    status: Boolean;

    @Column()
    // @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP(6)' })
    visibilityStart: String;


    @Column()
    // @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP(6)' })
    visibilityEnd: String;

    // @Column()
    // created_by: string;

    // @Column()
    // updated_by?: string;
}
